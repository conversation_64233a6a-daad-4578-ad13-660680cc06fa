<style>
    .menu-item {
        margin-bottom: 3px;
    }
    
    .menu-item .menu-link {
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }
    
    .menu-item .menu-link:hover {
        background: rgba(45, 53, 60, 0.08);
    }
    
    .menu-item.active .menu-link {
        background: #2d353c;
        color: #fff;
    }
    
    .menu-item .menu-icon {
        width: 30px;
        text-align: center;
        margin-right: 10px;
    }
    
    .menu-item .menu-text {
        font-weight: 500;
    }

    /* Submenu Styling */
    .menu-submenu {
        padding-left: 44px;
        margin-top: 3px;
    }

    .menu-submenu .menu-item .menu-link {
        padding: 6px 16px;
    }

    .menu-item.has-sub .menu-caret {
        transition: all 0.2s ease;
    }

    .menu-item.has-sub.active .menu-caret {
        transform: rotate(90deg);
    }
</style>

@if(hasPermissionInGuard('Data Aset - View'))
<div class="menu-item {{ request()->routeIs('asset-management.asset-hospital.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-hospital.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-boxes"></i>
        </div>
        <div class="menu-text">Data Aset</div>
    </a>
</div>
@endif

@if(hasPermissionInGuard('Penempatan & Document - View'))
<div class="menu-item {{ request()->routeIs('asset-management.asset-document.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-document.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-file-alt"></i>
        </div>
        <div class="menu-text">Penempatan dan Dokumen</div>
    </a>
</div>
@endif

<div class="menu-item {{ request()->routeIs('asset-management.asset-mutation.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-mutation.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-exchange-alt"></i>
        </div>
        <div class="menu-text">BAST Mutasi</div>
    </a>
</div>

@if (hasPermissionInGuard('Buku Bantu - View'))
<div class="menu-item {{ request()->routeIs('asset-management.helper-book.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.helper-book.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-book"></i>
        </div>
        <div class="menu-text">Buku Bantu</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Kategori Aset - View'))
<div class="menu-item {{ request()->routeIs('asset-management.asset-category.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-category.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-tags"></i>
        </div>
        <div class="menu-text">Kategori Aset</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Kartu Inventaris Ruangan - View'))
<div class="menu-item {{ request()->routeIs('asset-management.asset-position.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-position.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-clipboard-list"></i>
        </div>
        <div class="menu-text">Kartu Inventaris Ruangan</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Perubahan Posisi - View'))
<div class="menu-item {{ request()->routeIs('asset-management.position-change.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.position-change.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-map-marker-alt"></i>
        </div>
        <div class="menu-text">Perubahan Posisi</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Aset Rusak - View'))
<div class="menu-item {{ request()->routeIs('asset-management.damaged-asset.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.damaged-asset.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-exclamation-triangle"></i>
        </div>
        <div class="menu-text">Aset Rusak</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Penghapusan Aset - View'))
<div class="menu-item {{ request()->routeIs('asset-management.asset-deletion.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-deletion.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-trash-alt"></i>
        </div>
        <div class="menu-text">Data Penghapusan Aset</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('History Aset - View'))
<div class="menu-item {{ request()->routeIs('asset-management.asset-history.*') ? 'active' : '' }}">
    <a href="{{ route('asset-management.asset-history.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-history"></i>
        </div>
        <div class="menu-text">History Aset</div>
    </a>
</div>
@endif

@php
$canViewReport =
hasPermissionInGuard('Laporan Penerimaan Inventaris - View') ||
hasPermissionInGuard('Laporan Aspak - View') ||
hasPermissionInGuard('Laporan Daftar Barang - View') ||
hasPermissionInGuard('Laporan KIR - View') ||
hasPermissionInGuard('Laporan KIR Barang - View');
@endphp

@if ($canViewReport)
<div class="menu-item has-sub {{ request()->routeIs('asset-management.asset-report.*') ? 'active' : '' }}">
    <a href="javascript:;" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-file-alt"></i>
        </div>
        <div class="menu-text">Laporan Aset</div>
        <div class="menu-caret"></div>
    </a>
    <div class="menu-submenu">
        @if (hasPermissionInGuard('Laporan Penerimaan Inventaris - View'))
        <div class="menu-item {{ request()->routeIs('asset-management.asset-report.penerimaan_inventaris') ? 'active' : '' }}">
            <a href="{{ route('asset-management.asset-report.penerimaan_inventaris') }}" class="menu-link">
                <div class="menu-text">Penerimaan Inventaris</div>
            </a>
        </div>
        @endif

        @if (hasPermissionInGuard('Laporan Aspak - View'))
        <div class="menu-item {{ request()->routeIs('asset-management.asset-report.aspak') ? 'active' : '' }}">
            <a href="{{ route('asset-management.asset-report.aspak') }}" class="menu-link">
                <div class="menu-text">Aspak</div>
            </a>
        </div>
        @endif

        @if (hasPermissionInGuard('Laporan Daftar Barang - View'))
        <div class="menu-item {{ request()->routeIs('asset-management.asset-report.daftar_barang') ? 'active' : '' }}">
            <a href="{{ route('asset-management.asset-report.daftar_barang') }}" class="menu-link">
                <div class="menu-text">Daftar Barang</div>
            </a>
        </div>
        @endif

        @if (hasPermissionInGuard('Laporan KIR - View'))
        <div class="menu-item {{ request()->routeIs('asset-management.asset-report.kir') ? 'active' : '' }}">
            <a href="{{ route('asset-management.asset-report.kir') }}" class="menu-link">
                <div class="menu-text">KIR</div>
            </a>
        </div>
        @endif

        @if (hasPermissionInGuard('Laporan KIR Barang - View'))
        <div class="menu-item {{ request()->routeIs('asset-management.asset-report.kir_barang') ? 'active' : '' }}">
            <a href="{{ route('asset-management.asset-report.kir_barang') }}" class="menu-link">
                <div class="menu-text">KIR Barang</div>
            </a>
        </div>
        @endif
    </div>
</div>
@endif
