<style>
    .menu-item {
        margin-bottom: 3px;
    }
    
    .menu-item .menu-link {
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }
    
    .menu-item .menu-link:hover {
        background: rgba(45, 53, 60, 0.08);
    }
    
    .menu-item.active .menu-link {
        background: #2d353c;
        color: #fff;
    }
    
    .menu-item .menu-icon {
        width: 30px;
        text-align: center;
        margin-right: 10px;
    }
    
    .menu-item .menu-text {
        font-weight: 500;
    }

    /* Submenu Styling */
    .menu-submenu {
        padding-left: 44px;
        margin-top: 3px;
    }

    .menu-submenu .menu-item .menu-link {
        padding: 6px 16px;
    }

    .menu-item.has-sub .menu-caret {
        transition: all 0.2s ease;
    }

    .menu-item.has-sub.active .menu-caret {
        transform: rotate(90deg);
    }
</style>

@if (hasPermissionInGuard('Jadwal Alkes - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.alkes.schedule.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.alkes.schedule.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-calendar-alt"></i>
            </div>
            <div class="menu-text">Jadwal Alkes</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Pemeliharaan Alkes - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.alkes.activity.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.alkes.activity.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-tools"></i>
            </div>
            <div class="menu-text">Pemeliharaan Alkes</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Jadwal Non Alkes - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.non-alkes.schedule.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.non-alkes.schedule.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-calendar-check"></i>
            </div>
            <div class="menu-text">Jadwal NonAlkes</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Pemeliharaan Non Alkes - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.non-alkes.activity.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.non-alkes.activity.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-wrench"></i>
            </div>
            <div class="menu-text">Pemeliharaan NonAlkes</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Request Perbaikan - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.incidental.request.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.incidental.request.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-clipboard-list"></i>
            </div>
            <div class="menu-text">Request Perbaikan</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Aktivitas Perbaikan - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.incidental.activity.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.incidental.activity.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-cogs"></i>
            </div>
            <div class="menu-text">Aktivitas Perbaikan</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Putusan Perbaikan - View'))
    <div class="menu-item {{ request()->routeIs('maintenance-asset.incidental.decision.*') ? 'active' : '' }}">
        <a href="{{ route('maintenance-asset.incidental.decision.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-check-circle"></i>
            </div>
            <div class="menu-text">Putusan Perbaikan</div>
        </a>
    </div>
@endif

@php
    $canViewReport =
        hasPermissionInGuard('Laporan Pemeliharaan Jadwal - View') ||
        hasPermissionInGuard('Laporan Pemeliharaan - View') ||
        hasPermissionInGuard('Laporan Detail Pemeliharaan - View') ||
        hasPermissionInGuard('Laporan Isidental - View');
@endphp

@if ($canViewReport)
    <div class="menu-item has-sub {{ request()->routeIs('maintenance-asset.laporan.*') ? 'active' : 'closed' }}">
        <a href="javascript:;" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-file-alt"></i>
            </div>
            <div class="menu-text">Laporan Pemeliharaan</div>
            <div class="menu-caret"></div>
        </a>

        <div class="menu-submenu">
            @if (hasPermissionInGuard('Laporan Pemeliharaan Jadwal - View'))
                <div class="menu-item {{ request()->routeIs('maintenance-asset.laporan.report.jadwal') ? 'active' : '' }}">
                    <a href="{{ route('maintenance-asset.laporan.report.jadwal') }}" class="menu-link">
                        <div class="menu-text">Jadwal</div>
                    </a>
                </div>
            @endif
            @if (hasPermissionInGuard('Laporan Pemeliharaan - View'))
                <div class="menu-item {{ request()->routeIs('maintenance-asset.laporan.report.index') ? 'active' : '' }}">
                    <a href="{{ route('maintenance-asset.laporan.report.index') }}" class="menu-link">
                        <div class="menu-text">Pemeliharaan</div>
                    </a>
                </div>
            @endif
            @if (hasPermissionInGuard('Laporan Detail Pemeliharaan - View'))
                <div class="menu-item {{ request()->routeIs('maintenance-asset.laporan.report.detail') ? 'active' : '' }}">
                    <a href="{{ route('maintenance-asset.laporan.report.detail') }}" class="menu-link">
                        <div class="menu-text">Detail Pemeliharaan</div>
                    </a>
                </div>
            @endif
            @if (hasPermissionInGuard('Laporan Isidental - View'))
                <div class="menu-item {{ request()->routeIs('maintenance-asset.laporan.report.log_incidental') ? 'active' : '' }}">
                    <a href="{{ route('maintenance-asset.laporan.report.log_incidental') }}" class="menu-link">
                        <div class="menu-text">Perbaikan</div>
                    </a>
                </div>
            @endif
        </div>
    </div>
@endif

