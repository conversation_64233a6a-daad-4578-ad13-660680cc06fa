
@if (hasPermissionInGuard('Perencanaan - View'))
<div class="menu-item {{ request()->routeIs('planning.consumable-procurement.*') ? 'active' : '' }}">
    <a href="{{ route('planning.consumable-procurement.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-cubes"></i>
        </div>
        <div class="menu-text">Perencanaan</div>
    </a>
</div>
@endif
@if (hasPermissionInGuard('Approve Perencanaan - View'))
<div class="menu-item {{ request()->routeIs('planning.approval-consumable-procurement.*') ? 'active' : '' }}">
    <a href="{{ route('planning.approval-consumable-procurement.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-cubes"></i>
        </div>
        <div class="menu-text">Approve Perencanaan</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Pemeliharaan - View'))
<div class="menu-item {{ request()->routeIs('planning.asset-maintenance.*') ? 'active' : '' }}">
    <a href="{{ route('planning.asset-maintenance.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-cubes"></i>
        </div>
        <div class="menu-text">Pemeliharaan</div>
    </a>
</div>
@endif

@if (hasPermissionInGuard('Approve Pemeliharaan - View'))
<div class="menu-item {{ request()->routeIs('planning.approval-asset-maintenance.*') ? 'active' : '' }}">
    <a href="{{ route('planning.approval-asset-maintenance.index') }}" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-cubes"></i>
        </div>
        <div class="menu-text">Approve Pemeliharaan</div>
    </a>
</div>
@endif

@php
$canViewReport =
hasPermissionInGuard('Laporan Perencanaan - View') ||
hasPermissionInGuard('Laporan Pemeliharaan - View');
@endphp

@if ($canViewReport)
<div class="menu-item has-sub {{ request()->routeIs('planning.report.*') ? 'active' : 'closed' }}">
    <a href="javascript:;" class="menu-link">
        <div class="menu-icon">
            <i class="fa fa-file"></i>
        </div>
        <div class="menu-text">Laporan</div>
        <div class="menu-caret"></div>
    </a>

    <div class="menu-submenu {{ request()->routeIs('planning.report.*') ? '' : '' }}">
        @if (hasPermissionInGuard('Laporan Perencanaan - View'))    
        <div class="menu-item {{ request()->routeIs('planning.report.planning') ? 'active' : '' }}">
            <a href="{{ route('planning.report.planning') }}" class="menu-link">
                <div class="menu-text">Laporan Perencanaan</div>
            </a>
        </div>
        @endif
        @if (hasPermissionInGuard('Laporan Pemeliharaan - View'))
        <div class="menu-item {{ request()->routeIs('planning.report.maintenance') ? 'active' : '' }}">
            <a href="{{ route('planning.report.maintenance') }}" class="menu-link">
                <div class="menu-text">Laporan Pemeliharaan</div>
            </a>
        </div>
        @endif
    </div>
</div>
@endif