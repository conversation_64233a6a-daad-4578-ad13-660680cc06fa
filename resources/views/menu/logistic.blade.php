<style>
    .menu-item {
        margin-bottom: 3px;
    }

    .menu-item .menu-link {
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .menu-item .menu-link:hover {
        background: rgba(45, 53, 60, 0.08);
    }

    .menu-item.active .menu-link {
        background: #2d353c;
        color: #fff;
    }

    .menu-item .menu-icon {
        width: 30px;
        text-align: center;
        margin-right: 10px;
    }

    .menu-item .menu-text {
        font-weight: 500;
    }

    /* Submenu Styling */
    .menu-submenu {
        padding-left: 44px;
        margin-top: 3px;
    }

    .menu-submenu .menu-item .menu-link {
        padding: 6px 16px;
    }

    .menu-item.has-sub .menu-caret {
        transition: all 0.2s ease;
    }

    .menu-item.has-sub.active .menu-caret {
        transform: rotate(90deg);
    }
</style>

@if (hasPermissionInGuard('Daftar Aset Logistik - View'))
    <div class="menu-item {{ request()->routeIs('logistic.asset.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.asset.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-boxes"></i>
            </div>
            <div class="menu-text">Daftar Barang Logistik</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Barang Masuk - View'))
    <div class="menu-item {{ request()->routeIs('logistic.incoming-asset.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.incoming-asset.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-arrow-circle-down"></i>
            </div>
            <div class="menu-text">Barang Masuk</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Permintaan Barang - View'))
    <div class="menu-item {{ request()->routeIs('logistic.asset-request.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.asset-request.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-clipboard-list"></i>
            </div>
            <div class="menu-text">Permintaan Barang</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Realisasi Permintaan - View'))
    <div class="menu-item {{ request()->routeIs('logistic.asset-fulfillment.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.asset-fulfillment.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-check-circle"></i>
            </div>
            <div class="menu-text">Realisasi Permintaan</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Barang Keluar - View'))
    <div class="menu-item {{ request()->routeIs('logistic.outgoing-item.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.outgoing-item.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-arrow-circle-up"></i>
            </div>
            <div class="menu-text">Barang Keluar</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Penyesuaian Barang - View'))
    <div class="menu-item {{ request()->routeIs('logistic.stock-adjustment.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.stock-adjustment.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-balance-scale"></i>
            </div>
            <div class="menu-text">Penyesuaian Barang</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Laporan Stock Opname - View'))
    <div class="menu-item {{ request()->routeIs('logistic.report-logistic.stock-opname.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.report-logistic.stock-opname.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-clipboard-check"></i>
            </div>
            <div class="menu-text">Stock Opname Logistik</div>
        </a>
    </div>
@endif

@if (hasPermissionInGuard('Stock Opname Non Logistik - View'))
    <div class="menu-item {{ request()->routeIs('logistic.stock-recap.*') ? 'active' : '' }}">
        <a href="{{ route('logistic.stock-recap.index') }}" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-tasks"></i>
            </div>
            <div class="menu-text">Stock Opname Non Logistik</div>
        </a>
    </div>
@endif

@php
    $canViewReport =
        hasPermissionInGuard('Laporan Request Barang - View') ||
        hasPermissionInGuard('Laporan Barang Masuk - View') ||
        hasPermissionInGuard('Laporan Barang Keluar - View') ||
        hasPermissionInGuard('Laporan Resume Barang Keluar - View') ||
        hasPermissionInGuard('Laporan Kartu Stock - View') ||
        hasPermissionInGuard('Laporan Stock Opname Non Logistik - View');
@endphp

@if ($canViewReport)
    <div class="menu-item has-sub {{ (request()->routeIs('logistic.report-logistic.*') && (!request()->routeIs('logistic.report-logistic.stock-opname.*'))) ? 'active' : 'closed' }}">
        <a href="javascript:;" class="menu-link">
            <div class="menu-icon">
                <i class="fa fa-file-alt"></i>
            </div>
            <div class="menu-text">Laporan Logistik</div>
            <div class="menu-caret"></div>
        </a>

        <div class="menu-submenu">
            @if (hasPermissionInGuard('Laporan Request Barang - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.request.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.request.index') }}" class="menu-link">
                        <div class="menu-text">Request Barang</div>
                    </a>
                </div>
            @endif

            @if (hasPermissionInGuard('Laporan Barang Masuk - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.incoming.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.incoming.index') }}" class="menu-link">
                        <div class="menu-text">Barang Masuk</div>
                    </a>
                </div>
            @endif

            @if (hasPermissionInGuard('Laporan Barang Keluar - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.outgoing.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.outgoing.index') }}" class="menu-link">
                        <div class="menu-text">Barang Keluar</div>
                    </a>
                </div>
            @endif

            @if (hasPermissionInGuard('Laporan Penerimaan dan Pengeluaran Barang - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.inout.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.inout.index') }}" class="menu-link">
                        <div class="menu-text">Laporan Penerimaan dan Pengeluaran Barang</div>
                    </a>
                </div>
            @endif

            @if (hasPermissionInGuard('Laporan Resume Barang Keluar - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.resumeoutgoing.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.resumeoutgoing.index') }}" class="menu-link">
                        <div class="menu-text">Resume Barang Keluar</div>
                    </a>
                </div>
            @endif

            @if (hasPermissionInGuard('Laporan Kartu Stock - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.general-ledger.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.general-ledger.index') }}" class="menu-link">
                        <div class="menu-text">Kartu Stock</div>
                    </a>
                </div>
            @endif

            @if (hasPermissionInGuard('Laporan Stock Opname Non Logistik - View'))
                <div class="menu-item {{ request()->routeIs('logistic.report-logistic.recap.index') ? 'active' : '' }}">
                    <a href="{{ route('logistic.report-logistic.recap.index') }}" class="menu-link">
                        <div class="menu-text">Stock Opname RS</div>
                    </a>
                </div>
            @endif
        </div>
    </div>
@endif
