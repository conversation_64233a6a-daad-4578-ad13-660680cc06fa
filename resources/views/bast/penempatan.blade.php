<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <title>Document BAST Penempatan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 0mm;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid black;
        }

        .header img {
            width: 100px;
        }

        .header .title {
            text-align: center;
        }

        .header .title h2 {
            margin: 0;
        }

        .header .title p {
            margin: 5px 0 0;
            font-size: 12px;
        }

        .info {
            margin-left: 82%;
            font-size: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th {
            text-align: center;
        }

        th,
        td {
            border: 1px solid black;
            padding: 5px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 50px;
        }

        .footer .signature {
            width: 200px;
        }

        .footer .signature p {
            margin: 5px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header" style="text-align: center; margin-bottom: 10px;">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo"
                            style="width: 50px; display: block; margin: 0 auto;">
                    </td>
                    <td style="border: none;">
                        <div class="title" style="text-align: center;">
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div class="info">
            <span style="display: block; margin-bottom: 5px;">
                Jam : {{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->format('H.i') : '-' }}
            </span>

            <span>No : {{ $requestDocument->document_code ?? '-' }}</span>
        </div>

        <h6 style="text-align: center;"><u>BUKTI PENGAMBILAN BARANG DARI GUDANG</u></h6>

        <table class="table" style="font-size: 10px !important;">
            <thead>
                <tr>
                    <th rowspan="2" width="15%">Tgl. Penyerahan Brg. Menurut Permintaan</th>
                    <th rowspan="2" width="20%">Barang Diterima dari Gudang</th>
                    <th rowspan="2">Nama dan Kode Barang</th>
                    <th rowspan="2">Satuan</th>
                    <th colspan="2">Jumlah Barang</th>
                    <th rowspan="2">Harga</th>
                </tr>
                <tr>
                    <th>Angka</th>
                    <th>Huruf</th>
                </tr>
            </thead>

            <tbody style="font-size: 10px;">
                @foreach ($data as $item)
                <tr>
                    <td>{{ $item->received_date ?? '-' }}</td>
                    <td></td>
                    <td>
                        {{ $item->item_name ?? '-' }} <br>
                        {{ $item->room_code ?? "-" }} <br>
                        {{ $item->item_code ?? '-' }} <br>
                        {{ $item->register_codes ?? '-' }}
                    </td>
                    <td style="text-align: center;">{{ $item->uom_name ?? '-' }}</td>
                    <td style="text-align: center;">{{ $item->total_quantity ?? 0 }}</td>
                    <td style="text-align: center;">-</td>
                    <td>{{ 'Rp. ' . number_format($item->unit_price ?? 0, 0, ',', '.') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <div class="footer">
            <table style="border: none; font-size: 10px; text-align: center;">
                <tr style="border: none;">
                    <td style="width: 30%; text-align: center;border: none;">Yang Menerima</td>
                    <td style="width: 40%; text-align: center;border: none;"></td>
                    <td style="width: 30%; text-align: center;border: none;">Yang Menyerahkan</td>
                </tr>
                <tr style="border: none;">
                    <td colspan="2" style="width: 70%;border: none;"></td>
                    <td style="width: 30%; text-align: center;border: none;">Pengurus Barang Pengguna</td>
                </tr>
                <tr style="border: none;">
                    <td colspan="3" style="height: 30px;border: none;"></td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 30%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span style="text-decoration: underline;">{{ $requestDocument->party2_name ?? '......................' }}</span>
                    </td>
                    <td style="width: 40%; border: none;"></td>
                    <td style="width: 30%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span
                            style="text-decoration: underline;">{{ $mainPerson['assetManager']->employee_name ?? '......................' }}</span>
                    </td>
                </tr>


                <tr style="border: none;">
                    <td style="width: 30%;border: none;">NIP. {{ $requestDocument->party2_identification_number ?? '......................' }}</td>
                    <td style="width: 40%;border: none;"></td>
                    <td style="width: 30%;border: none;">{{ $mainPerson['assetManager']->employee_grade ?? '-' }}</td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 30%;border: none;"></td>
                    <td style="width: 40%;border: none;"></td>
                    <td style="width: 30%;border: none;">NIP.
                        {{ $mainPerson['assetManager']->employee_identification_number ?? '......................' }}
                    </td>
                </tr>
            </table>
        </div>

        <div style="text-align: center; font-size: 10px; margin-top: 30px;">
            <p>
                Mengetahui:
                <br>{{ $mainPerson['teamLeader']->employee_position ?? '-' }}
            </p>

            <div style="margin-top: 50px;">
                <span style="display: block;">
                    <strong><u>{{ $mainPerson['teamLeader']->employee_name ?? '......................' }}</u></strong>
                </span>
                <span style="display: block; margin: 10px 0px;">{{ $mainPerson['teamLeader']->employee_grade ?? '-' }}</span>
                <span style="display: block; ">NIP:
                    {{ $mainPerson['teamLeader']->employee_identification_number ?? '......................' }}</span>
            </div>


        </div>
    </div>

    <div class="container">
        <div class="header" style="text-align: center; margin-bottom: 10px;">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo"
                            style="width: 50px; display: block; margin: 0 auto;">
                    </td>
                    <td style="border: none;">
                        <div class="title" style="text-align: center;">
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div style="text-align: center; text-transform: uppercase; font-size: 10px;">
            <strong style="display: block;">berita acara</strong>
            <span style="display: block;">PENGAMBILAN BARANG INVENTARIS DARI GUDANG LOGISIK</span>
            <u>{{ config('app.report_header_hospital') }}</u>
        </div>

        <div style="margin-top: 20px; font-size: 10px;">
            <span style="display: block;">
                Kami yang bertanda tangan dibawah ini, Pada hari ini
                <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->translatedFormat('l') : '-' }}</b>,
                Tanggal <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->day : '-' }}</b>
                Bulan <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->translatedFormat('F') : '-' }}</b>
                Tahun <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->format('Y') : '-' }}</b>
            </span>


            <table style="border: none !important; border-collapse: collapse !important;" class="table">
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="15%">Nama</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $mainPerson['assetManager']->employee_name ?? '......................' }}</b></td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="15%">Jabatan</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $mainPerson['assetManager']->employee_position ?? '......................' }}</b>
                    </td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="15%">Alamat</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">{{ config('app.report_header_hospital') }}</td>
                </tr>
            </table>

            <span style="display: block; margin-top: -20px;">Selanjutnya disebut PIHAK PERTAMA</span>
        </div>

        <div style="margin-top: 20px; font-size: 10px;">
            <table style="border: none !important; border-collapse: collapse !important;" class="table">
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="15%">Nama</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party2_name ?? '......................' }}</b></td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="15%">Jabatan</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party2_position ?? '......................' }}</b></td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="15%">Alamat</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">{{ config('app.report_header_hospital') }}</td>
                </tr>
            </table>

            <span style="display: block; margin-top: -20px;">Selanjutnya disebut PIHAK KEDUA</span>
        </div>

        <div style="margin-top: 10px; font-size: 10px;">
            <p>PIHAK PERTAMA menyerahkan barang kepada PIHAK KEDUA, dan PIHAK KEDUA menyatakan telah menerima barang
                dari PIHAK PERTAMA berupa daftar terlampir :</p>

            <table class="table">
                <thead>
                    <tr>
                        <th width="5%">No.</th>
                        <th>Banyaknya</th>
                        <th>Nama Barang</th>
                        <th>Harga Satuan (Rp)</th>
                        <th>Jumlah (Rp)</th>
                        <th>Keterangan</th>
                    </tr>
                </thead>

                <tbody id="table-berita-acara" style="font-size: 10px;">
                    @foreach ($data as $item)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->total_quantity ?? 0 }} {{ $item->uom_name ?? '-' }}</td>
                        <td>{{ $item->item_name ?? '-' }}</td>
                        <td>{{ 'Rp.' . number_format($item->unit_price ?? 0, 0, ',', '.') }}</td>
                        <td>{{ 'Rp.' . number_format(($item->unit_price ?? 0) * ($item->total_quantity ?? 0), 0, ',', '.') }}</td>
                        <td>{{ $item->description ?? '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <p>
                Demikianlah berita acara serah terima barang ini di perbuat oleh kedua belah pihak, adapun barang-barang
                tersebut dalam keadaan baik dan cukup, sejak penandatanganan berita acara ini, maka barang tersebut,
                menjadi tanggung jawab PIHAK KEDUA, memelihara / merawat dengan baik serta dipergunakan untuk keperluan
                (tempat dimana barang itu dibutuhkan).
            </p>
        </div>

        <div class="footer">
            <table style="border: none; font-size: 10px; text-align: center;">
                <tr style="border: none;">
                    <td style="width: 30%; text-align: center;border: none;">Yang Menerima</td>
                    <td style="width: 40%; text-align: center;border: none;"></td>
                    <td style="width: 30%; text-align: center;border: none;">Pengurus Barang Pengguna</td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 30%; text-align: center;border: none;">Jabatan</td>
                    <td style="width: 40%; text-align: center;border: none;"></td>
                    <td style="width: 30%; text-align: center;border: none;">{{ config('app.hospital_name') }}</td>
                </tr>
                <tr style="border: none;">
                    <td colspan="3" style="height: 30px;border: none;"></td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 30%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span style="text-decoration: underline;">{{ $requestDocument->party2_name }}</span>
                    </td>
                    <td style="width: 40%; border: none;"></td>
                    <td style="width: 30%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span
                            style="text-decoration: underline;">{{ $mainPerson['assetManager']->employee_name }}</span>
                    </td>
                </tr>


                <tr style="border: none;">
                    <td style="width: 30%;border: none;">NIP.
                        {{ $requestDocument->party2_identification_number ?? '.................' }}
                    </td>
                    <td style="width: 40%;border: none;"></td>
                    <td style="width: 30%;border: none;">{{ $mainPerson['assetManager']->employee_grade ?? '-' }}</td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 30%;border: none;"></td>
                    <td style="width: 40%;border: none;"></td>
                    <td style="width: 30%;border: none;">NIP.
                        {{ $mainPerson['assetManager']->employee_identification_number ?? '......................' }}
                    </td>
                </tr>
            </table>
        </div>

        <div style="text-align: center; font-size: 10px; margin-top: 20px;">
            <p>
                Mengetahui:
                <br>{{ $mainPerson['headPlanning']->employee_position ?? '-' }}
            </p>

            <div style="margin-top: 50px;">
                <span style="display: block;">
                    <strong><u>{{ $mainPerson['headPlanning']->employee_name ?? '......................' }}</u></strong>
                </span>
                <span
                    style="display: block; margin: 10px 0px;">{{ $mainPerson['headPlanning']->employee_grade ?? '-' }}</span>
                <span style="display: block; ">NIP:
                    {{ $mainPerson['headPlanning']->employee_identification_number ?? '......................' }}</span>
            </div>
        </div>

    </div>
</body>

</html>