<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <title>Document BAST Mutasi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 0mm;
            box-sizing: border-box;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid black;
        }

        .header img {
            width: 100px;
        }

        .header .title {
            text-align: center;
        }

        .header .title h2 {
            margin: 0;
        }

        .header .title p {
            margin: 5px 0 0;
            font-size: 12px;
        }

        .info {
            margin-left: 82%;
            font-size: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th {
            text-align: center;
        }

        th,
        td {
            border: 1px solid black;
            padding: 5px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 50px;
        }

        .footer .signature {
            width: 200px;
        }

        .footer .signature p {
            margin: 5px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header" style="text-align: center; margin-bottom: 10px;">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo"
                            style="width: 50px; display: block; margin: 0 auto;">
                    </td>
                    <td style="border: none;">
                        <div class="title" style="text-align: center;">
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div class="info">
            <span style="display: block; margin-bottom: 5px;">
                Jam : {{ \Carbon\Carbon::parse($requestDocument->created_at)->format('H.i') }}
            </span>

            <span>No : {{ $requestDocument->document_code }}</span>
        </div>

        <h5 style="text-align: center;"><u>BERITA ACARA SERAH TERIMA BARANG INVENTARIS</u></h5>
        <div style="margin-top: 20px; font-size: 10px;">
            <span style="display: block;">
                Pada hari ini <b>{{ \Carbon\Carbon::parse($requestDocument->created_at)->translatedFormat('l') }}</b>,
                tanggal <b>{{ \Carbon\Carbon::parse($requestDocument->created_at)->day }}</b>,
                bulan <b>{{ \Carbon\Carbon::parse($requestDocument->created_at)->translatedFormat('F') }}</b>,
                tahun <b>{{ \Carbon\Carbon::parse($requestDocument->created_at)->format('Y') }}</b>. Kami yang bertanda
                tangan dibawah ini :
            </span>

            <table style="border: none !important; border-collapse: collapse !important;" class="table">
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%">1.</td>
                    <td style="border: none !important; padding: 0;" width="15%">Nama</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party1_name ?? '......................' }}</b>
                    </td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">NIP</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party1_identification_number ?? '......................' }}</b>
                    </td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Pangkat/Gol</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party1_grade ?? '......................' }}</b>
                    </td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Jabatan</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party1_position ?? '......................' }}</b>
                    </td>
                </tr>

            </table>

            <span style="display: block; margin-top: -15px;">Selanjutnya disebut PIHAK PERTAMA</span>
        </div>

        <div style="margin-top: 20px; font-size: 10px;">
            <table style="border: none !important; border-collapse: collapse !important;" class="table">
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%">2.</td>
                    <td style="border: none !important; padding: 0;" width="15%">Nama</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party2_name }}</b></td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">NIP</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party2_identification_number ?? '......................' }}</b>
                    </td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Pangkat/Gol</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party2_grade ?? '......................' }}</b>
                    </td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="1%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Jabatan</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;">
                        <b>{{ $requestDocument->party2_position ?? '......................' }}</b>
                    </td>
                </tr>

            </table>

            <span style="display: block; margin-top: -15px;">Selanjutnya disebut PIHAK KEDUA</span>
        </div>

        <div style="margin-top: 10px; font-size: 10px;">
            <p>PIHAK PERTAMA telah menyerahkan / mutasikan barang dibawah ini kepada PIHAK KEDUA dari ruangan
                {{ $initialRoom->room_name ?? '-' }}.
                Ke ruangan {{ $targetRoom->room_name ?? '-' }}. Berupa :
            </p>
            <table class="table">
                <thead>
                    <tr>
                        <th width="5%">No.</th>
                        <th>Nama Barang / Kode Barang</th>
                        <th>Merk/Type</th>
                        <th>Jumlah</th>
                        <th>Tahun Pembelian</th>
                    </tr>
                </thead>

                <tbody id="table-berita-acara" style="font-size: 10px;">
                    @foreach ($data as $item)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->item_name ?? '-' }} / {{ $item->item_code ?? '-' }}</td>
                        <td>{{ $item->asset_entry_item_name ?? '-' }}</td>
                        <td>{{ $item->total_quantity ?? 1 }}</td>
                        <td>{{ $item->received_date ? \Carbon\Carbon::parse($item->received_date)->format('Y') : '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <p>
                Dalam kondisi ( Baik / Rusak Ringan / Rusak Berat )
            </p>
            <p>
                Demikian Berita Acara Serah Terima ini dibuat untuk dapat dipergunakan sebagaimana mestinya.

                (tempat dimana barang itu dibutuhkan).
            </p>
        </div>

        <div class="footer">
            <table style="border: none; font-size: 10px; text-align: center;">
                <tr style="border: none;">
                    <td style="width: 30%; text-align: center;border: none;">PIHAK KEDUA</td>
                    <td style="width: 40%; text-align: center;border: none;"></td>
                    <td style="width: 30%; text-align: center;border: none;">PIHAK PERTAMA</td>
                </tr>

                <tr style="border: none;">
                    <td colspan="3" style="height: 30px;border: none;"></td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 30%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span style="text-decoration: underline;">({{ $requestDocument->party2_name }})</span>
                    </td>
                    <td style="width: 40%; border: none;"></td>
                    <td style="width: 30%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span style="text-decoration: underline;">({{ $requestDocument->party1_name }})</span>
                    </td>
                </tr>


                <tr style="border: none;">
                    <td style="width: 30%;border: none;">NIP. {{ $requestDocument->party2_identification_number }}
                    </td>
                    <td style="width: 40%;border: none;"></td>
                    <td style="width: 30%;border: none;">NIP. {{ $requestDocument->party1_identification_number }}
                    </td>
                </tr>
            </table>
        </div>

        <div style="text-align: center; font-size: 10px; margin-top: 20px;">
            <p>
                MENGETAHUI:
                <br>{{ $mainPerson['assetManager']->employee_position ?? '-' }}
            </p>

            <div style="margin-top: 50px;">
                <span style="display: block;">
                    <strong><u>{{ $mainPerson['assetManager']->employee_name ?? '-' }}</u></strong>
                </span>

                <span style="display: block; ">NIP:
                    {{ $mainPerson['assetManager']->employee_identification_number ?? '-' }}</span>
            </div>
        </div>
    </div>

</body>

</html>