<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <title>Document BAST Rusak</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 0mm;
            box-sizing: border-box;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid black;
        }

        .header img {
            width: 100px;
        }

        .header .title {
            text-align: center;
        }

        .header .title h2 {
            margin: 0;
        }

        .header .title p {
            margin: 5px 0 0;
            font-size: 12px;
        }

        .info {
            margin-left: 82%;
            font-size: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th {
            text-align: center;
        }

        th,
        td {
            border: 1px solid black;
            padding: 5px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 50px;
        }

        .footer .signature {
            width: 200px;
        }

        .footer .signature p {
            margin: 5px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="" style="text-align: center; margin-bottom: 10px;">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo"
                            style="width: 50px; display: block; margin: 0 auto;">
                    </td>
                    <td style="border: none;">
                        <div class="title" style="text-align: center;">
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div class="info">
            <span style="display: block; margin-bottom: 5px;">
                Jam : {{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->format('H.i') : '-' }}
            </span>

            <span>No : {{ $requestDocument->document_code ?? '-' }}</span>
        </div>


        <h5 style="text-align: center;">BERITA ACARA SERAH TERIMA</h5>
        <div style="margin-top: 20px; font-size: 10px;">
            <span style="display: block;">
                Pada hari ini <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->translatedFormat('l') : '-' }}</b>,
                tanggal <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->day : '-' }}</b>,
                bulan <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->translatedFormat('F') : '-' }}</b>,
                tahun <b>{{ $requestDocument->created_at ? \Carbon\Carbon::parse($requestDocument->created_at)->format('Y') : '-' }}</b>,
                dilakukan serah terima barang bekas/rusak berat inventaris dari ruang <b>{{ $initialRoom->room_name ?? '-' }}</b>,
                ke Bagian Logistik yang dilakukan oleh:
            </span>
            <table style="border: none !important; border-collapse: collapse !important;" class="table">
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="2%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Nama</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party1_name ?? '......................' }}</b></td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="2%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">NIP</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party1_identification_number ?? '......................' }}</b>
                    </td>
                </tr>

                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="2%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Jabatan</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party1_position ?? '......................' }}</b></td>
                </tr>

            </table>


            <span style="display: block; margin-top: -15px;">Selanjutnya disebut <b>PIHAK I</b></span>
        </div>

        <div style="margin-top: 20px; font-size: 10px;">
            <table style="border: none !important; border-collapse: collapse !important;" class="table">
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="2%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Nama</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party2_name ?? '......................' }}</b></td>
                </tr>
                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="2%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">NIP</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party2_identification_number ?? '......................' }}</b>
                    </td>
                </tr>

                <tr style="border: none !important;">
                    <td style="border: none !important; padding: 0;" width="2%"></td>
                    <td style="border: none !important; padding: 0;" width="15%">Jabatan</td>
                    <td style="border: none !important;" width="1%">:</td>
                    <td style="border: none !important;"><b>{{ $requestDocument->party2_position ?? '......................' }}</b></td>
                </tr>
            </table>

            <span style="display: block; margin-top: -15px;">Selanjutnya disebut <b>PIHAK II</b></span>
        </div>

        <div style="margin-top: 10px; font-size: 10px;">
            <span style="display: block;">
                Bahwa PIHAK I telah menyerahkan barang inventaris dari ruangan <b>{{ $initialRoom->room_name ?? '-' }}</b>,
                kepada PIHAK II berupa:
            </span>

            <table class="table">
                <thead>
                    <tr>
                        <th width="5%">No.</th>
                        <th>Nama Barang</th>
                        <th>Merk/Type</th>
                        <th>Tahun Barang</th>
                        <th>Kode Barang</th>
                    </tr>
                </thead>

                <tbody id="table-berita-acara" style="font-size: 10px;">
                    @foreach ($data as $item)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $item->item_name ?? '-' }}</td>
                        <td>{{ $item->asset_entry_item_name ?? '-' }}</td>
                        <td>{{ $item->received_date ? \Carbon\Carbon::parse($item->received_date)->format('Y') : '-' }}</td>
                        <td>{{ $item->item_code ?? '-' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>


            <p>
                Demikian Berita Acara Serah Terima ini dibuat agar dapat dipergunakan sebagaimana mestinya.
            </p>
        </div>

        <div class="footer">
            <table style="border: none; font-size: 10px; text-align: center;">
                <tr style="border: none;">
                    <td colspan="2" style="text-align: center;border: none;"></td>
                    <td style="text-align: left; border: none;">
                        Ditetapkan di &nbsp;&nbsp;: Pontianak<br>
                        Tanggal &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;: ……..
                    </td>

                </tr>
                <tr style="border: none;">
                    <td style="width: 20%; text-align: center;border: none;">Yang Menyerahkan <br> Pihak I</td>
                    <td style="width: 60%; text-align: center;border: none;"></td>
                    <td style="width: 20%; text-align: center;border: none;">Yang Menerima <br> Pihak II</td>
                </tr>
                {{-- <tr style="border: none;">
                <td style="width: 20%; text-align: center;border: none;">Pihak I </td>
                <td style="width: 60%; text-align: center;border: none;"></td>
                <td style="width: 20%; text-align: center;border: none;">Pihak II</td>
            </tr> --}}

                <tr style="border: none;">
                    <td colspan="3" style="height: 30px;border: none;"></td>
                </tr>
                <tr style="border: none;">
                    <td style="width: 20%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span style="text-decoration: underline;">{{ $requestDocument->party1_name ?? '......................' }}</span>
                    </td>
                    <td style="width: 60%; border: none;"></td>
                    <td style="width: 20%; border: none; font-weight: bold; padding-bottom: 5px;">
                        <span style="text-decoration: underline;">{{ $requestDocument->party2_name ?? '......................' }}</span>
                    </td>
                </tr>


                <tr style="border: none;">
                    <td style="width: 20%;border: none;">NIP. {{ $requestDocument->party1_identification_number ?? '......................' }}
                    </td>
                    <td style="width: 60%;border: none;"></td>
                    <td style="width: 20%;border: none;">NIP. {{ $requestDocument->party2_identification_number ?? '......................' }}
                    </td>
                </tr>
            </table>
        </div>


    </div>

</body>

</html>