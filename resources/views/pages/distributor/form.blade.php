@extends("layouts.app")
@push("style")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="{{ $action }}" method="post" id="formdata">
            @method($method)
            @csrf
            <input type="hidden" name="add" id="add" value="0">

            <div class="form-group mb-3">
                <label for="nama_distributor" class="form-label">Nama Distributor</label>
                <input type="text" class="form-control" id="nama_distributor" name="nama_distributor" value="{{ old('nama_distributor', $distributor->nama_distributor) }}">

                @error('nama_distributor')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="alamat" class="form-label">Alamat</label>
                <input type="text" class="form-control" id="alamat" name="alamat" value="{{ old('alamat', $distributor->alamat) }}">

                @error('alamat')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="kontak" class="form-label">Kontak</label>
                <input type="text" class="form-control" id="kontak" name="kontak" value="{{ old('kontak', $distributor->kontak) }}">

                @error('kontak')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" value="{{ old('email', $distributor->email) }}">

                @error('email')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i> Simpan & Tambah</button>
                <a href="{{ route('kategori.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script>
    $("#btn-save").on("click", function() {
        $("#formdata").submit();
    });

    $("#btn-save-add").on("click", function() {
        $("#add").val(1);
        $("#formdata").submit();
    });
</script>
@endpush
