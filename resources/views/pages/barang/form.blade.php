@extends("layouts.app")
@push("style")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="{{ $action }}" method="post" id="formdata">
            @method($method)
            @csrf
            <input type="hidden" name="add" id="add" value="0">

            <div class="form-group mb-3">
                <label for="kode_barang" class="form-label">Kode Barang</label>
                <input type="text" class="form-control" id="kode_barang" name="kode_barang" value="{{ old('kode_barang', $barang->kode_barang) }}">

                @error('kode_barang')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="nama_barang" class="form-label"><PERSON><PERSON></label>
                <input type="text" class="form-control" id="nama_barang" name="nama_barang" value="{{ old('nama_barang', $barang->nama_barang) }}">

                @error('nama_barang')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="kategori" class="form-label">Kategori</label>
                <select name="kategori" id="kategori" class="form-select">
                    <option value="" selected>Pilih Kategori</option>
                    @foreach($kategori as $kat)
                    <option value="{{ $kat->id }}" @if(old('id', $barang->kategori_id) == $kat->id) selected @endif>{{ $kat->nama_kategori }}</option>
                    @endforeach
                </select>

                @error('kategori')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i> Simpan & Tambah</button>
                <a href="{{ route('kategori.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script>
    $("#btn-save").on("click", function() {
        $("#formdata").submit();
    });

    $("#btn-save-add").on("click", function() {
        $("#add").val(1);
        $("#formdata").submit();
    });
</script>
@endpush
