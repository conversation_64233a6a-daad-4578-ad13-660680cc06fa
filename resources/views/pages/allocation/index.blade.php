@extends("layouts.app")
@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <button class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Save Data</button>
        </div>

        <form id="form-allocation">
            <div class="row" id="card-allocation"></div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $.ajax({
            url: "{{ route('allocation.list') }}",
            type: "POST",
            data: {
                asset_id: localStorage.getItem("asset_id")
            },
            success: function(response) {
                let allocation = response.data;

                $("#card-allocation").empty()

                if (Array.isArray(allocation) && allocation.length > 0) {
                    // Loop through each allocation item and append it to the card container
                    $.each(allocation, function(i, data) {
                        // Create the card content
                        const cardHtml = `
                        <input type="hidden" name="asset_id[]" value="${data.id}">
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-3">
                                        <div class="d-block">
                                            <strong class="d-block">Kode Barang :</strong>
                                            <span>${data.barang.kode_barang}</span>
                                        </div>
                                        <div class="d-block text-end">
                                            <strong class="d-block">Nama Barang :</strong>
                                            <span>${data.barang.nama_barang}</span>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mb-3">
                                        <div class="d-block">
                                            <strong class="d-block">Register Code :</strong>
                                            <span>${data.register_code}</span>
                                        </div>
                                        <div class="d-block text-end">
                                            <strong class="d-block">QR Code :</strong>
                                            <span>${data.qr_code}</span>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-group">
                                            <label for="peruntukan-${i}"><strong>Peruntukan :</strong></label>
                                            <select name="peruntukan[]" id="peruntukan-${i}" class="form-select select-ruangan">
                                                <option value="">Select Peruntukan</option>
                                            </select>

                                            <span class="d-block mt-1 text-danger" id="error_peruntukan_${i}"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>`;

                        $("#card-allocation").append(cardHtml);

                        // Apply Select2 to the newly created select element
                        $(`#peruntukan-${i}`).select2({
                            ajax: {
                                url: "{{ route('ruangan.list') }}",
                                dataType: 'json',
                                delay: 250,
                                data: function(params) {
                                    return {
                                        q: params.term, // Search query
                                        page: params.page || 1
                                    };
                                },
                                processResults: function(data, params) {
                                    params.page = params.page || 1;
                                    return {
                                        results: $.map(data.data, function(item) {
                                            return {
                                                id: item.id,
                                                text: item.kode_ruangan + " - " + item.nama_ruangan
                                            };
                                        }),
                                        pagination: {
                                            more: data.current_page < data.last_page
                                        }
                                    };
                                },
                                cache: true
                            },
                            placeholder: 'Pilih Ruangan',
                            minimumInputLength: 0,
                            allowClear: true
                        });
                    });
                } else {
                    $("#card-allocation").append(`<p>No allocations found.</p>`);
                }
            }
        });

        $(document).on("click", "#btn-save", function(e) {
            e.preventDefault();

            let formdata = new FormData($("#form-allocation")[0]);

            $.ajax({
                url: "{{ route('allocation.store') }}",
                type: "POST",
                data: formdata,
                processData: false,
                contentType: false,
                success: function(response) {
                    successMessage(response.message);
                    localStorage.removeItem("asset_id");
                    setTimeout(function() {
                        window.location.href = "{{ route('asset.index') }}";
                    }, 1000)
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $.each(errors, function(key, value) {
                            var fieldKey = key.replace(/\./g, '_');

                            $("#" + fieldKey).addClass("is-invalid");
                            $("#error_" + fieldKey).text(value[0]);
                        });
                    } else {
                        errorMessage(xhr.responseJSON.message)
                    }
                }
            });
        });
    });
</script>
@endpush
