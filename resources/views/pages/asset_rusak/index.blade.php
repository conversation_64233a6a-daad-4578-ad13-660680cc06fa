@extends("layouts.app")
@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-danger me-1 d-none" id="btn-bulk"><i class="fas fa-trash me-1"></i>Hapus</button>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-striped w-100" id="datatable">
                <thead>
                    <tr>
                        <th>No.</th>
                        <th>QR Code</th>
                        <th>Tag RFID</th>
                        <th>Nama <PERSON>ang</th>
                        <th>Merk/Type</th>
                        <th>Kode Barang</th>
                        <th>Kode Lokasi</th>
                        <th>Tanggal Perolehan</th>
                        <th>Asal Perolehan</th>
                        <th>Tanggal BAST Rusak</th>
                        <th>Action</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script>
    $('#datatable').DataTable({
        responsive: true,
    });
</script>
@endpush