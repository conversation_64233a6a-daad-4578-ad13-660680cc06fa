@extends("layouts.app")
@push("style")
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="{{ $action }}" method="post" class="row" id="formdata">
            @method($method)
            @csrf
            <input type="hidden" name="add" id="add" value="0">

            <div class="form-group col-md-4 mb-3">
                <label for="tanggal_barang_masuk" class="form-label">Tanggal Barang Masuk</label>
                <input type="date" name="tanggal_barang_masuk" id="tanggal_barang_masuk" class="form-control" value="{{ old('tanggal_barang_masuk') ?? now()->format('Y-m-d') }}">

                <span class="d-block text-danger" id="error_tanggal_barang_masuk"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="rekening_belanja" class="form-label">Rekening Belanja</label>
                <input type="text" name="rekening_belanja" id="rekening_belanja" class="form-control" value="{{ old('rekening_belanja') }}" placeholder="Enter rekening belanja">

                <span class="d-block text-danger" id="error_rekening_belanja"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="tanggal_pembayaran" class="form-label">Tanggal Pembayaran</label>
                <input type="date" name="tanggal_pembayaran" id="tanggal_pembayaran" class="form-control" value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">

                <span class="d-block text-danger" id="error_tanggal_pembayaran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_bast_kontrak" class="form-label">No. BAST Kontrak</label>
                <input type="text" name="no_bast_kontrak" id="no_bast_kontrak" class="form-control" value="{{ old('no_bast_kontrak') }}" placeholder="Enter no. bast kontrak">

                <span class="d-block text-danger" id="error_no_bast_kontrak"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_bast_pembayaran" class="form-label">No. BAST Pembayaran</label>
                <input type="text" name="no_bast_pembayaran" id="no_bast_pembayaran" class="form-control" value="{{ old('no_bast_pembayaran') }}" placeholder="Enter no. bast pembayaran">

                <span class="d-block text-danger" id="error_no_bast_pembayaran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="asal_perolehan" class="form-label">Asal Perolehan</label>
                <select name="asal_perolehan" id="asal_perolehan" class="form-select asal-perolehan">
                    <option value="">-- Pilih Asal Perolehan --</option>
                </select>

                <span class="d-block text-danger" id="error_asal_perolehan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="distributor" class="form-label">Distributor</label>
                <select name="distributor" id="distributor" class="form-select distributor w-100">
                    <option value="">-- Pilih Distributor --</option>
                </select>

                <span class="d-block text-danger" id="error_distributor"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kategori" class="form-label">Kategori</label>
                <select name="kategori" id="kategori" class="form-select kategori w-100">
                    <option value="">-- Pilih Kategori --</option>
                </select>

                <span class="d-block text-danger" id="error_kategori"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kode_barang" class="form-label">Kode Barang</label>
                <select name="kode_barang" id="kode_barang" class="form-select kode-barang">
                    <option value="">-- Pilih Kode Barang --</option>
                </select>

                <span class="d-block text-danger" id="error_kode_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_barang" class="form-label">Nama Barang</label>
                <input type="text" name="nama_barang" id="nama_barang" class="form-control" value="{{ old('nama_barang') }}" placeholder="Enter nama barang">

                <span class="d-block text-danger" id="error_nama_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_umum" class="form-label">Nama Umum</label>
                <input type="text" name="nama_umum" id="nama_umum" class="form-control" value="{{ old('nama_umum') }}" placeholder="Enter nama umum">

                <span class="d-block text-danger" id="error_nama_umum"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="merk" class="form-label">Merk</label>
                <input type="text" name="merk" id="merk" class="form-control" value="{{ old('merk') }}" placeholder="Enter merk">

                <span class="d-block text-danger" id="error_merk"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="tipe" class="form-label">Tipe/Model</label>
                <input type="text" name="tipe" id="tipe" class="form-control" value="{{ old('tipe') }}" placeholder="Enter tipe">

                <span class="d-block text-danger" id="error_tipe"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_akd" class="form-label">No. AKD/AKL</label>
                <input type="text" name="no_akd" id="no_akd" class="form-control" value="{{ old('no_akd') }}" placeholder="Enter no. akd">

                <span class="d-block text-danger" id="error_no_akd"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="spesifikasi_umum" class="form-label">Spesifikasi Umum</label>
                <input type="text" name="spesifikasi_umum" id="spesifikasi_umum" class="form-control" value="{{ old('spesifikasi_umum') }}" placeholder="Enter spesifikasi umum">

                <span class="d-block text-danger" id="error_spesifikasi_umum"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="bahan" class="form-label">Bahan</label>
                <input type="text" name="bahan" id="bahan" class="form-control" value="{{ old('bahan') }}" placeholder="Enter bahan">

                <span class="d-block text-danger" id="error_bahan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="ukuran" class="form-label">Ukuran</label>
                <input type="text" name="ukuran" id="ukuran" class="form-control" value="{{ old('ukuran') }}" placeholder="Enter ukuran">

                <span class="d-block text-danger" id="error_ukuran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="satuan_barang" class="form-label">Satuan Barang</label>
                <select name="satuan_barang" id="satuan_barang" class="form-select satuan">
                    <option value="">-- Pilih Satuan Barang --</option>
                </select>

                <span class="d-block text-danger" id="error_satuan_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="harga_satuan" class="form-label">Harga Satuan</label>
                <input type="text" name="harga_satuan" id="harga_satuan" class="form-control" value="{{ old('harga_satuan') }}" placeholder="Enter harga satuan">

                <span class="d-block text-danger" id="error_harga_satuan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="jumlah_barang" class="form-label">Jumlah Barang</label>
                <input type="number" name="jumlah_barang" id="jumlah_barang" class="form-control" value="{{ old('jumlah_barang') }}" readonly placeholder="Enter jumlah barang">

                <span class="d-block text-danger" id="error_jumlah_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="total_harga" class="form-label">Total Harga</label>
                <input type="text" name="total_harga" id="total_harga" class="form-control" value="{{ old('total_harga') }}" placeholder="Enter total harga" readonly>

                <span class="d-block text-danger" id="error_total_harga"></span>
            </div>

            <div class="form-group col-md-4 mb-5">
                <label for="kondisi" class="form-label">Kondisi</label>
                <select name="kondisi" id="kondisi" class="form-select">
                    <option value="">-- Pilih Kondisi --</option>
                    <option value="Baik">Baik</option>
                    <option value="Rusak Ringan">Rusak Ringan</option>
                    <option value="Rusak Berat ">Rusak Berat</option>
                </select>

                <span class="d-block text-danger" id="error_kondisi"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="keterangan" class="form-label">Keterangan</label>
                <input type="text" name="keterangan" id="keterangan" class="form-control" value="{{ old('keterangan') }}" placeholder="Enter keterangan">

                <span class="d-block text-danger" id="error_keterangan"></span>
            </div>

            <div class="col-md-12">
                <div class="accordion" id="accordion">
                </div>
            </div>

            <div class="form-group col-md-12 mt-3">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i> Simpan & Tambah</button>
                <a href="{{ route('kategori.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script>
    function formatRupiah(value) {
        let numberString = value.toString().replace(/[^,\d]/g, '');
        let split = numberString.split(',');
        let remainder = split[0].length % 3;
        let rupiah = split[0].substr(0, remainder);
        let thousands = split[0].substr(remainder).match(/\d{3}/g);

        if (thousands) {
            let separator = remainder ? '.' : '';
            rupiah += separator + thousands.join('.');
        }
        rupiah = split[1] !== undefined ? rupiah + ',' + split[1] : rupiah;
        return rupiah;
    }

    $(document).on("keyup", "#harga_satuan", function() {
        let hargaSatuan = $(this).val().replace(/\./g, '');
        let jumlahBarang = parseInt($("#jumlah_barang").val()) || 0;
        let totalHarga = hargaSatuan * jumlahBarang;

        $(this).val(formatRupiah(hargaSatuan));
        $("#total_harga").val(formatRupiah(totalHarga));
    });

    $(document).on("keyup", "#jumlah_barang", function() {
        let hargaSatuan = $("#harga_satuan").val().replace(/\./g, '');
        let jumlahBarang = parseInt($(this).val()) || 0;
        let totalHarga = hargaSatuan * jumlahBarang;

        $("#total_harga").val(formatRupiah(totalHarga));
    });

    $(".kategori").select2({
        ajax: {
            url: "{{ route('kategori.list') }}",
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function(item) {
                        return {
                            id: item.id,
                            text: item.nama_kategori
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: 'Pilih Kategori',
        minimumInputLength: 0,
        allowClear: true
    });

    $(".kode-barang").select2({
        ajax: {
            url: "{{ route('barang.list') }}",
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function(item) {
                        return {
                            id: item.id,
                            text: item.kode_barang + ' - ' + item.nama_barang
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: 'Pilih Kode Barang',
        minimumInputLength: 0,
        allowClear: true
    });

    $(".distributor").select2({
        ajax: {
            url: "{{ route('distributor.list') }}",
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function(item) {
                        return {
                            id: item.id,
                            text: item.nama_distributor
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: 'Pilih Distributor',
        minimumInputLength: 0,
        allowClear: true
    });

    $(".asal-perolehan").select2({
        ajax: {
            url: "{{ route('asalperolehan.list') }}",
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function(item) {
                        return {
                            id: item.id,
                            text: item.nama_asal
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: 'Pilih Asal Perolehan',
        minimumInputLength: 0,
        allowClear: true
    });

    $(".satuan").select2({
        ajax: {
            url: "{{ route('satuan.list') }}",
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                return {
                    results: $.map(data.data, function(item) {
                        return {
                            id: item.id,
                            text: item.nama_satuan
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: 'Pilih Satuan',
        minimumInputLength: 0,
        allowClear: true
    });

    $("#kode_barang").on("change", function() {
        var kode_barang = $(this).val();
        $("#jumlah_barang").removeAttr("readonly");
    });

    $(document).on("keyup", "#jumlah_barang", function() {
        let count = parseInt($(this).val());

        if (count == 0) {
            return false;
        }

        let kode_barang = $("#kode_barang").val();

        if (!kode_barang) {
            alert("Silakan pilih kode barang terlebih dahulu");
        }

        $('#accordion').find('.dynamic-accordion').remove();

        $.ajax({
            type: "GET",
            method: "GET",
            url: "{{ route('asset.generate.code') }}",
            data: {
                kode_barang: kode_barang,
                jumlah_barang: count
            },
            success: function(response) {
                let registercodes = response.register_codes;

                $.each(registercodes, function(i, data) {
                    let color = i % 2 == 0 ? "text-danger" : "text-orange";
                    let newAccordionItem = `
                        <div class="accordion-item dynamic-accordion">
                            <div class="accordion-header" id="heading${i + 1}">
                                <button class="accordion-button bg-cyan-600 text-white px-3 py-10px pointer-cursor" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${i + 1}">
                                    <i class="fa fa-circle fa-fw ${color} me-2 fs-8px"></i> Form Input ${i + 1}
                                </button>
                            </div>
                            <div id="collapse${i + 1}" class="accordion-collapse collapse" data-bs-parent="#accordion">
                                <div class="accordion-body text-white row">
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="register_code_${i + 1}" class="form-label text-dark">Register Code</label>
                                        <input type="text" name="register_code[]" id="register_code_${i + 1}" class="form-control" placeholder="Enter register code" value="${data.register_code}">

                                        <span class="d-block text-danger" id="error_register_code"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="no_sn_${i + 1}" class="form-label text-dark">No. SN</label>
                                        <input type="text" name="no_sn[]" id="no_sn_${i + 1}" class="form-control" placeholder="Enter no. sn">

                                        <span class="d-block text-danger" id="error_no_sn"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="qr_code_${i + 1}" class="form-label text-dark">QR Code</label>
                                        <input type="text" name="qr_code[]" id="qr_code_${i + 1}" class="form-control" placeholder="Enter qr code" readonly value="${data.qr_code}">

                                        <span class="d-block text-danger" id="error_qr_code"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="tag_rfid_${i + 1}" class="form-label text-dark">Tag RFID</label>
                                        <input type="text" name="tag_rfid[]" id="tag_rfid_${i + 1}" class="form-control" placeholder="Enter tag rfid">

                                        <span class="d-block text-danger" id="error_tag_rfid"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="no_rangka_${i + 1}" class="form-label text-dark">No. Rangka</label>
                                        <input type="text" name="no_rangka[]" id="no_rangka_${i + 1}" class="form-control" placeholder="Enter no rangka" readonly>

                                        <span class="d-block text-danger" id="error_no_rangka"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="no_mesin_${i + 1}" class="form-label text-dark">No. Mesin</label>
                                        <input type="text" name="no_mesin[]" id="no_mesin_${i + 1}" class="form-control" placeholder="Enter no mesin" readonly>

                                        <span class="d-block text-danger" id="error_no_mesin"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="no_polisi_${i + 1}" class="form-label text-dark">No. Polisi</label>
                                        <input type="text" name="no_polisi[]" id="no_polisi_${i + 1}" class="form-control" placeholder="Enter no polisi" readonly>

                                        <span class="d-block text-danger" id="error_no_polisi"></span>
                                    </div>
                                    <div class="form-group col-md-4 mb-3">
                                        <label for="no_bpkb_${i + 1}" class="form-label text-dark">No. BPKB</label>
                                        <input type="text" name="no_bpkb[]" id="no_bpkb_${i + 1}" class="form-control" placeholder="Enter no bpkb" readonly>

                                        <span class="d-block text-danger" id="error_no_bpkb"></span>
                                    </div>
                                </div>
                            </div>
                        </div>`;

                    $('#accordion').append(newAccordionItem);
                })
            }
        })
    });

    $("#btn-save").on("click", function(e) {
        e.preventDefault();
        let formData = new FormData($("#formdata")[0]);

        $.ajax({
            type: "POST",
            url: "{{ route('asset.store') }}",
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                $('#formdata')[0].reset();
                $('#accordion').empty();
                successMessage(response.message);
                window.location.href = "{{ route('asset.index') }}";
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $("#" + key).addClass("is-invalid");
                        $("#error_" + key).text(value);
                    });
                } else {
                    errorMessage(xhr.responseJSON.message)
                }
            }
        });
    });

    $("#btn-save-add").on("click", function(e) {
        e.preventDefault();
        let formData = new FormData($("#formdata")[0]);

        $.ajax({
            type: "POST",
            url: "{{ route('asset.store') }}",
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                $('#formdata')[0].reset();
                $('#accordion').empty();
                successMessage(response.message);
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(key, value) {
                        $("#" + key).addClass("is-invalid");
                        $("#error_" + key).text(value);
                    });
                } else {
                    errorMessage(xhr.responseJSON.message)
                }
            }
        });
    });
</script>
@endpush