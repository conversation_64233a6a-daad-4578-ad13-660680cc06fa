@extends("layouts.app")
@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <div class="d-flex justify-content-end mb-3">
            <button type="button" class="btn btn-danger me-1 d-none" id="btn-bulk"><i class="fas fa-trash me-1"></i>Hapus</button>
            <button type="button" class="btn btn-success me-1 d-none" id="btn-alokasi"><i class="fas fa-forward me-1"></i>Alokasi Asset</button>
            <a href="{{ route('asset.create') }}" class="btn btn-primary"><i class="fas fa-plus-circle me-1"></i>Tambah Asset</a>
        </div>

        <div class="table-responsive">
            {{ $dataTable->table(["class" => "table table-striped table-bordered w-100"]) }}
        </div>
    </div>
</div>

<x-delete></x-delete>
<x-bulk-delete :action="route('asset.bulkdelete')"></x-bulk-delete>
<!-- Offcanvas -->

@endsection

@push("script")
{{ $dataTable->scripts(attributes: ['type' => 'module']) }}
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script>
    $(document).on("click", "#btn-alokasi", function() {
        let selectedItems = [];
        const checkedCheckboxes = $("#datatable").find(".checkbox-id:checked");

        if (checkedCheckboxes.length > 0) {
            checkedCheckboxes.each(function() {
                const itemId = $(this).val();
                selectedItems.push(itemId);
            });
        }

        let uniqueItems = Array.from(new Set(selectedItems));
        localStorage.setItem("asset_id", JSON.stringify(uniqueItems));

        window.location.href = "{{ route('asset.allocation') }}";
    });
</script>
@endpush
