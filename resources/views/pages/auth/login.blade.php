<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Sensi | Asset Management</title>
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.png') }}">
    <link rel="icon" href="{{ asset('assets/images/favicon.png') }}">
    <meta name="description" content="Sensi - Aplikasi Management Asset & Logistik rumah sakit">

    <meta property="og:title" content="Sensi | Asset Management" />
    <meta property="og:image" content="{{ asset('assets/images/favicon.png') }}" />
    <meta property="og:description" content="Aplikasi Management Asset & Logistik rumah sakit" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />

    <!-- ================== BEGIN core-css ================== -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <link href="{{ asset('/') }}css/vendor.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/default/app.min.css" rel="stylesheet" />
    <!-- ================== END core-css ================== -->

    <link rel="stylesheet" href="{{ asset('/') }}plugins/toastify/toastify.css">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Open Sans', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .login-container {
            display: flex;
            min-height: 100vh;
        }

        .left-section {
            flex: 1;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            color: white;
            position: relative;
        }

        .left-content {
            text-align: center;
            max-width: 500px;
        }

        .main-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .main-description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .feature-boxes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            width: 100%;
        }

        .feature-box {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 0.8rem;
        }

        .feature-icon {
            font-size: 1.8rem;
            color: #4facfe;
        }

        .feature-content h4 {
            margin: 0 0 0.3rem 0;
            font-weight: 600;
            font-size: 0.95rem;
        }

        .feature-content p {
            margin: 0;
            opacity: 0.8;
            font-size: 0.8rem;
            line-height: 1.3;
        }

        .right-section {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.6rem 0.8rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            height: 40px;
        }

        .form-control:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        }

        .form-check {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .form-check-input {
            margin-right: 0.5rem;
        }

        .form-check-label {
            color: #2c3e50;
            font-weight: 500;
        }

        .forgot-password {
            color: #4facfe;
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-password:hover {
            color: #2a5298;
            text-decoration: underline;
        }

        .btn-signin {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-signin:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
            color: white;
        }

        .help-text {
            text-align: center;
            margin-top: 1.5rem;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .security-notice {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .security-icon {
            color: #1976d2;
            font-size: 1.2rem;
        }

        .security-text {
            color: #1976d2;
            font-size: 0.9rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
            }

            .left-section {
                padding: 2rem 1rem;
                min-height: 40vh;
                order: 2;
            }

            .right-section {
                padding: 1rem;
                order: 1;
            }

            .main-title {
                font-size: 2rem;
            }

            .feature-boxes {
                grid-template-columns: 1fr;
            }

            .login-card {
                padding: 2rem;
            }
        }
    </style>
</head>

<body>
    <div id="loader" class="app-loader">
        <span class="spinner"></span>
    </div>

    <div id="app" class="app">
        <div class="login-container">
            <!-- Left Section -->
            <div class="left-section">
                <div class="left-content">
                    <h1 class="main-title">Sensi</h1>
                    <h3>(Sensor Identifikasi)</h3>
                    <p class="main-description">Aplikasi Management Asset & Logistik rumah sakit</p>

                    <div class="feature-boxes">
                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="bi bi-box-seam"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Manajemen Asset</h4>
                                <p>Kelola dan monitor semua aset rumah sakit dengan sistem tracking yang terintegrasi</p>
                            </div>
                        </div>

                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="bi bi-tools"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Pemeliharaan Asset</h4>
                                <p>Jadwalkan dan lakukan pemeliharaan rutin untuk memastikan aset tetap optimal</p>
                            </div>
                        </div>

                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Logistik Asset</h4>
                                <p>Kelola distribusi dan pergerakan aset antar unit dengan tracking real-time</p>
                            </div>
                        </div>

                        <div class="feature-box">
                            <div class="feature-icon">
                                <i class="bi bi-calendar-check"></i>
                            </div>
                            <div class="feature-content">
                                <h4>Perencanaan Asset</h4>
                                <p>Rencanakan pengadaan dan pengembangan aset berdasarkan kebutuhan rumah sakit</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Section -->
            <div class="right-section">
                <div class="login-card">
                    <div class="login-header">
                        <img src="/assets/images/logo-sensi.png" style="width: 80%;">
                    </div>

                    <form action="{{ route('login') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="username" class="form-label">Email Address</label>
                            <input type="text" name="username" class="form-control" id="username" placeholder="Enter your email address" />
                            @error('email')
                            <div class="d-block text-danger mt-1">
                                {{ $message }}
                            </div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <div class="password-field">
                                <input type="password" name="password" class="form-control" id="password" placeholder="Enter your password" />
                            </div>
                            @error('password')
                            <div class="d-block text-danger mt-1">
                                {{ $message }}
                            </div>
                            @enderror
                        </div>

                        <div class="form-check">
                            <div>
                                <input class="form-check-input" name="remember" type="checkbox" value="" id="rememberMe" />
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-signin">
                            Sign In
                            <i class="bi bi-arrow-right"></i>
                        </button>
                    </form>

                    {{-- <div class="help-text">
                        Need help? Contact IT Support
                    </div> --}}

                    {{-- <div class="security-notice">
                        <div class="security-icon">
                            <i class="bi bi-shield-lock"></i>
                        </div>
                        <div class="security-text">
                            Security Notice: Your data is protected with enterprise-grade security and HIPAA compliance.
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>

        <!-- BEGIN scroll-top-btn -->
        <a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <!-- ================== BEGIN core-js ================== -->
    <script src="{{ asset('/') }}js/vendor.min.js"></script>
    <script src="{{ asset('/') }}js/app.min.js"></script>
    <script src="{{ asset('/') }}js/theme/default.min.js"></script>
    <!-- ================== END core-js ================== -->

    <script src="{{ asset('/') }}plugins/toastify/toastify.js"></script>

    @if(session("error"))
    <script>
        Toastify({
            text: "{{ session('error') }}",
            duration: 2000,
            style: {
                background: "#c62828"
            }
        }).showToast();
    </script>
    @endif
</body>

</html>