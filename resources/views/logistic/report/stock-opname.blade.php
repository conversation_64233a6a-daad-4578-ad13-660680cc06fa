@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
<style>
/* ========================================
   REPORT CONTAINER & SCROLL STYLES
   ======================================== */

/* Report Container */
.report-container {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: auto;
    background: #fff;
}

/* Custom Scrollbar for Report Container */
.report-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.report-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.report-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.report-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Horizontal scrollbar styling */
.report-container::-webkit-scrollbar:horizontal {
    height: 8px;
}

.report-container::-webkit-scrollbar-track:horizontal {
    background: #f1f1f1;
    border-radius: 4px;
}

.report-container::-webkit-scrollbar-thumb:horizontal {
    background: #c1c1c1;
    border-radius: 4px;
}

.report-container::-webkit-scrollbar-thumb:horizontal:hover {
    background: #a8a8a8;
}

/* ========================================
   REPORT HEADER STYLES
   ======================================== */

.report-header {
    padding: 20px 24px;
    background: #fff;
    margin-bottom: 0;
}

.report-title {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    text-align: center;
}

.report-period {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0 0 16px 0;
    text-align: center;
}

.report-timestamp {
    font-size: 11px;
    color: #6c757d;
    text-align: right;
    margin: 0;
    font-style: italic;
}
</style>
@endpush

@push("menu")
@include("menu.logistic")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in stock opname report">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success btn-modern-rounded dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download"></i>
                    <span>Export</span>
                    <i class="fas fa-chevron-down ms-1"></i>
                </button>
                <ul class="dropdown-menu datatable-dropdown-menu">
                    <li><a class="dropdown-item datatable-dropdown-item btn-export-excel" href="javascript:;">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </a></li>
                    <li><a class="dropdown-item datatable-dropdown-item btn-export-pdf" href="javascript:;">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </a></li>
                </ul>
            </div>
            <div class="datatable-filter-icon btn-modern-rounded">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="report-container">
        <div class="report-header">
            <h2 class="report-title">LAPORAN STOCK OPNAME</h2>
            <p class="report-period" id="reportPeriod">PERIODE: <span id="periodText">-</span></p>
            <p class="report-timestamp" id="reportTimestamp">Laporan Dibuat: <span id="timestampText">-</span></p>
        </div>
        <div id="target-report">
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Periode</label>
                    <input type="text" id="datepicker" name="daterange" class="form-control">
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">Jenis Barang</label>
                    <select class="form-select stock_recap" name="stock_recap" id="stock_recap">
                        <option value="ALL">Semua Jenis Barang</option>
                        @foreach($configStockRecap as $recap)
                            <option value="{{ $recap->id }}">{{ $recap->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="d-flex gap-2 mt-4">
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFilter">
                        <i class="fas fa-undo me-1"></i>Reset Filter
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" id="applyFilter">
                        <i class="fas fa-filter me-1"></i>Terapkan Filter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/logistic/stockOpname.js') }}"></script>
@endpush