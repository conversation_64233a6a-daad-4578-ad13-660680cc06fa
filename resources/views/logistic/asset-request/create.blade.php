@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
    <style>
        #qr-scanner {
            border: 2px solid #007bff;
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
        }
        
        #qr-scanner:focus {
            border-color: #0056b3;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.5);
        }
        
        .qr-scanner-container {
            position: relative;
        }
        
        .qr-scanner-container .input-group-text {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        #btn-toggle-scanner {
            transition: all 0.3s ease;
        }
        
        #btn-toggle-scanner.btn-primary {
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
        }
        
        #qr-scanner-container {
            transition: all 0.3s ease;
        }
        
        #btn-focus-scanner {
            transition: all 0.2s ease;
        }
        
        #btn-focus-scanner:hover {
            background-color: #007bff;
            color: white;
        }
        
        #qr-scanner-container {
            width: auto !important;
        }
        
        #qr-scanner {
            width: 250px !important;
        }
        
        /* Mobile view fixes */
        @media (max-width: 768px) {
            .form-group {
                width: 100% !important;
            }
            
            .form-group .form-select,
            .form-group .form-control {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .select2-container {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .select2-container--default .select2-selection--single {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            .col-md-4 {
                flex: 0 0 100% !important;
                max-width: 100% !important;
            }
            
            .col-md-6 {
                flex: 0 0 100% !important;
                max-width: 100% !important;
            }
            
            /* Ensure all form elements have consistent width */
            .form-select,
            .form-control,
            .select2-container,
            .select2-selection {
                box-sizing: border-box !important;
                width: 100% !important;
                max-width: 100% !important;
            }
            
            /* Fix Select2 dropdown width in mobile */
            .select2-dropdown {
                width: 100% !important;
                max-width: 100% !important;
                min-width: 100% !important;
            }
            
            .select2-results__options {
                width: 100% !important;
                max-width: 100% !important;
            }
            
            /* Consistent spacing for mobile */
            .row.g-3 > .col-md-4 {
                margin-bottom: 1rem !important;
            }
            
            /* Ensure form groups don't overflow */
            .form-group {
                overflow: hidden !important;
            }
        }
    </style>
@endpush

@push("menu")
    @include("menu.logistic")
@endpush

@section("content")
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <h4 class="mb-0">{{ $title ?? "Blank page" }}</h4>
            </div>
            <div class="datatable-action-buttons">
                <a href="/logistik/permintaan-barang" class="btn btn-outline-secondary btn-modern-rounded">
                    <i class="fas fa-arrow-left"></i>
                    <span>Kembali</span>
                </a>
            </div>
        </div>

        <form action="" method="post" id="form-activity">
            <div class="row g-3 mb-4">
                <div class="form-group col-md-4">
                    <label for="target_room" class="form-label fw-semibold">Ruangan</label>
                    <select name="target_room" id="target_room" class="form-select target_room">
                        <option value="">Pilih Ruangan</option>
                    </select>
                    <span class="d-block text-danger small mt-1" id="error_target_room"></span>
                </div>

                <div class="form-group col-md-4">
                    <label for="field_option" class="form-label fw-semibold">Bidang Permintaan</label>
                    <select name="field_option" id="field_option" class="form-select field_option">
                        <option value="">Pilih Bidang</option>
                        @foreach($dataConfigStockField as $field)
                            <option value="{{ $field->id }}">{{ $field->name }}</option>
                        @endforeach
                    </select>
                    <span class="d-block text-danger small mt-1" id="error_field_option"></span>
                </div>

                <div class="form-group col-md-4">
                    <label for="request_date" class="form-label fw-semibold">Tanggal Request</label>
                    <input type="date" name="request_date" id="request_date" class="form-control" value="{{ old('request_date') ?? now()->format('Y-m-d') }}">
                    <span class="d-block text-danger small mt-1" id="error_request_date"></span>
                </div>
            </div>

            <div class="mb-3">
                <div class="row g-3">
                    <div class="col-md-6">
                        <button type="button" id="btn-add-row" class="btn btn-outline-primary btn-modern-rounded">
                            <i class="fas fa-plus-circle me-2"></i>Tambah Baris
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" id="btn-toggle-scanner" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-qrcode me-1"></i>
                            <span id="scanner-status">Aktifkan QR Scanner</span>
                        </button>
                        <div class="input-group qr-scanner-container mt-2" id="qr-scanner-container" style="display: none; max-width: 400px; margin-left: auto;">
                            <span class="input-group-text">
                                <i class="fas fa-qrcode"></i>
                            </span>
                            <input type="text" id="qr-scanner" class="form-control" placeholder="Scan QR Code atau ketik kode barang...">
                            <button type="button" id="btn-clear-scanner" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </button>
                            <button type="button" id="btn-focus-scanner" class="btn btn-outline-primary" title="Fokus ke QR Scanner">
                                <i class="fas fa-crosshairs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped w-100 mb-3">
                    <thead>
                    <tr>
                        <th style="max-width: 300px !important;">Barang Habis Pakai</th>
                        <th>Detail Barang</th>
                        <th>Qty</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody id="table-allocation"></tbody>
                </table>
            </div>

            <div class="row g-3 my-4">
                <div class="form-group col-md-6">
                    <label for="notes" class="form-label fw-semibold">Catatan</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Catatan / Deskripsi"></textarea>
                    <span class="d-block text-danger small mt-1" id="error_notes"></span>
                </div>

                <div class="form-group col-md-6">
                    <label for="attachment_link" class="form-label fw-semibold">
                        File Lampiran
                        <small class="text-muted">(*jika dibutuhkan)</small>
                    </label>
                    <span style="cursor: pointer" id="file_attach" class="d-block mb-2">
                        <i class="fa fa-file-pdf"></i> Pilih File
                    </span>
                    <input type="file" name="document" id="attachment_link" class="form-control" style="display: none;">
                    <span class="d-block text-danger small mt-1" id="error_document"></span>
                </div>
            </div>

            <div class="form-group d-flex justify-content-end">
                <button type="button" id="btn-save" class="btn btn-primary btn-modern-rounded">
                    <i class="fas fa-save me-2"></i>Simpan Form
                </button>
            </div>
        </form>
    </div>
@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/js/app/logistic/createRequestAsset.js') }}"></script>
@endpush
