@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
        rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
    <style>
        /* Responsive Grid Layout for Asset Labels */
        .asset-responsive-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            padding: 20px 0;
        }

        /* Tablet: 2 columns */
        @media (min-width: 768px) {
            .asset-responsive-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Desktop: 3 columns maximum */
        @media (min-width: 1024px) {
            .asset-responsive-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* Desktop: 4 columns maximum */
        @media (min-width: 1440px) {
            .asset-responsive-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Asset grid item styling */
        .asset-grid-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 200px;
            text-align: center;
            overflow: hidden;
        }

        /* Scale content based on container size to prevent overflow */
        .asset-grid-item .content-wrapper {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            transform-origin: center;
        }

        /* Responsive scaling for different screen sizes */
        @media (max-width: 640px) {
            .asset-grid-item .content-wrapper {
                transform: scale(0.8);
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {
            .asset-grid-item .content-wrapper {
                transform: scale(0.85);
            }
        }

        @media (min-width: 769px) and (max-width: 1023px) {
            .asset-grid-item .content-wrapper {
                transform: scale(0.9);
            }
        }

        @media (min-width: 1024px) {
            .asset-grid-item .content-wrapper {
                transform: scale(0.75);
            }
        }

        @media (min-width: 1440px)  {
            .asset-grid-item .content-wrapper {
                transform: scale(1);
            }
        }

        /* Ensure QR codes and labels scale properly */
        .asset-grid-item svg,
        .asset-grid-item img {
            max-width: 100%;
            height: auto;
        }

        .asset-grid-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Empty state should span full width */
        #empty-state {
            grid-column: 1 / -1;
        }

        /* Remove button styling */
        .asset-grid-item .btn-remove-asset {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .asset-grid-item:hover .btn-remove-asset {
            opacity: 1;
        }

        /* Center content inside asset items */
        .asset-grid-item .content-wrapper {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        /* QR Container styling */
        .qr-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 40px;
            min-width: 40px;
        }

        .qr-container canvas {
            display: block;
            margin: 0 auto;
        }
    </style>
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <h4 class="mb-0">{{ $title ?? 'Print Label BHP' }}</h4>
        </div>
    </div>

    <!-- Form Tambah Aset Logistik -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-plus me-2"></i>Tambah Aset Logistik untuk Print Label
            </h5>
        </div>
        <div class="card-body">
            <form id="form-add-asset">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="asset" class="form-label">Aset Logistik</label>
                            <select class="form-control" id="asset" name="asset" required>
                                <option value="">Pilih Aset Logistik</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="qty" class="form-label">QTY</label>
                            <input type="number" class="form-control" id="qty" name="qty" min="1" max="50" value="1" required>
                            <small class="form-text text-muted">Maksimal 50</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary btn-block w-100" id="btn-add-asset">
                                <i class="fas fa-plus me-2"></i>Add
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Asset List for Printing -->
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Daftar Aset Logistik untuk Print Label</h5>
            <div class="print-mode-selector">
                <div class="btn-group" role="group" aria-label="Print Mode">
                    <input type="radio" class="btn-check" name="print-mode" id="print-mode-label" value="label" checked>
                    <label class="btn btn-outline-primary btn-sm" for="print-mode-label">
                        <i class="fas fa-tag me-1"></i>Label
                    </label>

                    <input type="radio" class="btn-check" name="print-mode" id="print-mode-qrcode" value="qrcode">
                    <label class="btn btn-outline-primary btn-sm" for="print-mode-qrcode">
                        <i class="fas fa-qrcode me-1"></i>QR Code
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="asset-grid-container" class="asset-responsive-grid">
                <div class="text-center text-muted py-4" id="empty-state">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>Belum ada aset logistik yang dipilih untuk print label</p>
                    <p class="small">Pilih aset logistik dari form di atas untuk menambahkan ke daftar print</p>
                </div>
            </div>
        </div>
        <div class="card-footer text-center">
            <button type="button" class="btn btn-danger btn-lg me-3" id="btn-clear-all" disabled>
                <i class="fas fa-trash-alt me-2"></i>
                <span>Clear All</span>
            </button>
            <button type="button" class="btn btn-success btn-lg" id="btn-print" disabled>
                <i class="fas fa-print me-2"></i>
                <span>Print Label Aset Logistik Terpilih</span>
            </button>
            <p class="text-muted small mt-2 mb-0">Tombol akan aktif setelah memilih minimal 1 aset logistik</p>
        </div>
    </div>

</div>

@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/qr/qrcode.min.js"></script>
    <script src="{{ asset('/js/app/logistic/printLabelLogistic.js') }}"></script>
@endpush
