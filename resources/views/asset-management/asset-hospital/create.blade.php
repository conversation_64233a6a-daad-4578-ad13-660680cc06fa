@extends("layouts.app")
@push("style")
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <style>
        /* Custom styles for asset tabs */
        .asset-tabs .nav-link {
            border: 1px solid #dee2e6;
            border-bottom: none;
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 500;
            padding: 12px 20px;
            margin-right: 5px;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
            transition: all 0.3s ease;
        }

        .asset-tabs .nav-link:hover {
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .asset-tabs .nav-link.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
            font-weight: 600;
        }

        .asset-tabs .nav-link:not(.active):hover {
            background-color: #e9ecef;
        }

        .asset-tabs .nav-item:first-child .nav-link {
            margin-left: 0;
        }

        /* Tab content styling */
        .tab-content.asset-tab-content {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 20px;
            border-bottom-left-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
            background-color: #fff;
        }

        /* Placeholder content styling */
        .tab-placeholder {
            text-align: center;
            padding: 40px 20px;
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            border: 1px dashed #dee2e6;
        }

        .tab-placeholder h5 {
            color: #007bff;
            margin-bottom: 15px;
        }

        .tab-placeholder p {
            color: #6c757d;
            font-size: 1rem;
        }
        
        /* Accordion preview styling */
        .accordion-preview {
            background-color: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 0.375rem;
        }

        .accordion-preview .accordion-item {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
        }

        .accordion-preview .accordion-button {
            background-color: #e9ecef;
            color: #495057;
            pointer-events: none;
        }

        .accordion-preview .form-control:disabled,
        .accordion-preview .form-select:disabled {
            background-color: #e9ecef;
            opacity: 1;
        }
    </style>
@endpush

@push("menu")
    @include("menu.asset_management")
@endpush

@section("content")
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
        </div>
        <div class="panel-body">
            <form action="" method="post" class="row" id="formdata">
                @csrf
                <input type="hidden" name="add" id="add" value="0">

                <!-- Tab Navigation -->
                <ul class="nav asset-tabs" id="assetTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="info-umum-tab" data-bs-toggle="tab"
                                data-bs-target="#info-umum" type="button" role="tab" aria-controls="info-umum"
                                aria-selected="true">Informasi Umum
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="info-aspak-tab" data-bs-toggle="tab" data-bs-target="#info-aspak"
                                type="button" role="tab" aria-controls="info-aspak" aria-selected="false">Informasi
                            ASPAK
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="info-siap-bmd-tab" data-bs-toggle="tab"
                                data-bs-target="#info-siap-bmd" type="button" role="tab" aria-controls="info-siap-bmd"
                                aria-selected="false">Informasi SIAP BMD
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content asset-tab-content" id="assetTabContent">
                    <!-- Tab 1: Informasi Umum -->
                    <div class="tab-pane fade show active" id="info-umum" role="tabpanel"
                         aria-labelledby="info-umum-tab">
                        <!-- Preview Section -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card border-primary mb-4" id="data-preview-card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Preview Data Asset</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 mb-2">
                                                <small class="text-muted d-block">Tanggal Pembayaran</small>
                                                <span id="preview-tanggal-pembayaran" class="fw-bold text-dark">-</span>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <small class="text-muted d-block">Kode Barang</small>
                                                <span id="preview-kode-barang" class="fw-bold text-dark">-</span>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <small class="text-muted d-block">Nama Barang</small>
                                                <span id="preview-nama-barang" class="fw-bold text-dark">-</span>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <small class="text-muted d-block">Nama Barang di Kontrak</small>
                                                <span id="preview-nama-barang-kontrak"
                                                      class="fw-bold text-dark">-</span>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <small class="text-muted d-block">Jumlah Barang</small>
                                                <span id="preview-jumlah-barang" class="fw-bold text-dark">-</span>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <small class="text-muted d-block">Keterangan</small>
                                                <span id="preview-keterangan" class="fw-bold text-dark">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <!-- Section 1: Informasi Pengadaan -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-shopping-cart me-2"></i>Informasi Pengadaan</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="tanggal_barang_masuk" class="form-label">Tanggal Barang Masuk</label>
                                                                <input type="date" name="tanggal_barang_masuk" id="tanggal_barang_masuk"
                                                                       class="form-control" readonly disabled
                                                                       value="{{ old('tanggal_barang_masuk') ?? now()->format('Y-m-d') }}">

                                <span class="d-block text-danger" id="error_tanggal_barang_masuk"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="tanggal_pembayaran" class="form-label">Tanggal Pembayaran</label>
                                                                <input type="date" name="tanggal_pembayaran" id="tanggal_pembayaran"
                                                                       class="form-control" readonly disabled
                                                                       value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Tahun Pengadaan" di tab Informasi ASPAK dan "Tahun" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_tanggal_pembayaran"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="no_bast_kontrak" class="form-label">No. Kontrak/SP</label>
                                <input type="text" name="no_bast_kontrak" id="no_bast_kontrak" class="form-control"
                                       value="{{ old('no_bast_kontrak') }}" placeholder="Enter No. Kontrak/SP">

                                <span class="d-block text-danger" id="error_no_bast_kontrak"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="no_bast_pembayaran" class="form-label">No. BA Pembayaran</label>
                                <input type="text" name="no_bast_pembayaran" id="no_bast_pembayaran"
                                       class="form-control" value="{{ old('no_bast_pembayaran') }}"
                                       placeholder="Enter No. BA Pembayaran">

                                <span class="d-block text-danger" id="error_no_bast_pembayaran"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="asal_perolehan" class="form-label">Asal Perolehan</label>
                                <select name="asal_perolehan" id="asal_perolehan" class="form-select">
                                    <option value="" disabled>-- Pilih Asal Perolehan --</option>
                                    <option value="apbn">APBN</option>
                                    <option value="apbd">APBD</option>
                                    <option value="hibah">Hibah/Pemberian</option>
                                    <option value="kso">KSO/Pinjam</option>
                                    <option value="blu">BLU/BLUD</option>
                                    <option value="jkn">JKN</option>
                                    <option value="dak">DAK</option>
                                    <option value="sisoin">SISOIN</option>
                                    <option value="swasta">Swasta/Swadana</option>
                                </select>

                                <span class="d-block text-danger" id="error_asal_perolehan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="distributor" class="form-label">Distributor</label>
                                <select name="distributor" id="distributor" class="form-select distributor w-100">
                                    <option value="">-- Pilih Distributor --</option>
                                </select>

                                <span class="d-block text-danger" id="error_distributor"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="contract_form" class="form-label">Bentuk Kontrak</label>
                                <input type="text" name="contract_form" id="contract_form" class="form-control"
                                       placeholder="Masukkan Bentuk Kontrak">
                            </div>

                            <!-- Section 2: Informasi Rekening -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-money-check-alt me-2"></i>Informasi Pekerjaan</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="rekening_belanja" class="form-label">Uraian Pekerjaan</label>
                                <input type="text" name="rekening_belanja" id="rekening_belanja" class="form-control"
                                       value="{{ old('rekening_belanja') }}" placeholder="Enter Uraian Pekerjaan">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Keterangan" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_rekening_belanja"></span>
                            </div>


                            <!-- Section 4: Informasi Barang -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-box me-2"></i>Informasi Barang</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="kategori" class="form-label">Kategori</label>
                                <select name="kategori" id="kategori" class="form-select kategori w-100">
                                    <option value="">-- Pilih Kategori --</option>
                                </select>

                                <span class="d-block text-danger" id="error_kategori"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="kode_barang" class="form-label">Kode Barang</label>
                                <select name="kode_barang" id="kode_barang" class="form-select">
                                    <option value="">-- Pilih Kode Barang --</option>
                                </select>
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Kode Barang" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_kode_barang"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="nama_barang" class="form-label">Nama Barang</label>
                                <input type="text" name="nama_barang" id="nama_barang" class="form-control"
                                       value="{{ old('nama_barang') }}" placeholder="Enter nama barang">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Nama Aset" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_nama_barang"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="nama_barang_kontrak" class="form-label">Nama Barang di Kontrak</label>
                                <input type="text" name="nama_umum" id="nama_barang_kontrak" class="form-control"
                                       value="{{ old('nama_umum') }}" placeholder="Enter nama barang di kontrak">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Nama Umum" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_nama_barang_kontrak"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="merk" class="form-label">Merk</label>
                                <input type="text" name="merk" id="merk" class="form-control" value="{{ old('merk') }}"
                                       placeholder="Enter merk">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Merk Alat" di tab Informasi ASPAK</small>

                                <span class="d-block text-danger" id="error_merk"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="tipe" class="form-label">Tipe/Model</label>
                                <input type="text" name="tipe" id="tipe" class="form-control" value="{{ old('tipe') }}"
                                       placeholder="Enter tipe">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Merk Tipe (KFA)" di tab Informasi ASPAK</small>

                                <span class="d-block text-danger" id="error_tipe"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="no_akd" class="form-label">No. AKD/AKL</label>
                                <input type="text" name="no_akd" id="no_akd" class="form-control"
                                       value="{{ old('no_akd') }}" placeholder="Enter no. akd">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "No. AKL/AKD" di tab Informasi ASPAK</small>

                                <span class="d-block text-danger" id="error_no_akd"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="spesifikasi_umum" class="form-label">Spesifikasi Umum</label>
                                <input type="text" name="spesifikasi_umum" id="spesifikasi_umum" class="form-control"
                                       value="{{ old('spesifikasi_umum') }}" placeholder="Enter spesifikasi umum">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Spesifikasi" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_spesifikasi_umum"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="bahan" class="form-label">Bahan</label>
                                <input type="text" name="bahan" id="bahan" class="form-control"
                                       value="{{ old('bahan') }}" placeholder="Enter bahan">

                                <span class="d-block text-danger" id="error_bahan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="ukuran" class="form-label">Ukuran</label>
                                <input type="text" name="ukuran" id="ukuran" class="form-control"
                                       value="{{ old('ukuran') }}" placeholder="Enter ukuran">

                                <span class="d-block text-danger" id="error_ukuran"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="satuan_barang" class="form-label">Satuan Barang</label>
                                <select name="satuan_barang" id="satuan_barang" class="form-select satuan">
                                    <option value="">-- Pilih Satuan Barang --</option>
                                </select>

                                <span class="d-block text-danger" id="error_satuan_barang"></span>
                            </div>

                            <!-- Section 5: Informasi Finansial -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-calculator me-2"></i>Informasi Finansial</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="harga_satuan" class="form-label">Harga Satuan</label>
                                <input type="text" name="harga_satuan" id="harga_satuan" class="form-control"
                                       value="{{ old('harga_satuan') }}" placeholder="Enter harga satuan">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Harga Perolehan" di tab Informasi ASPAK dan "Harga" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_harga_satuan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="jumlah_barang" class="form-label">Jumlah Barang</label>
                                <input type="number" name="jumlah_barang" id="jumlah_barang" class="form-control"
                                       value="{{ old('jumlah_barang') }}" readonly placeholder="Enter jumlah barang">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Jumlah" di tab Informasi SIAP BMD</small>

                                <span class="d-block text-danger" id="error_jumlah_barang"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="total_harga" class="form-label">Total Harga</label>
                                <input type="text" name="total_harga" id="total_harga" class="form-control"
                                       value="{{ old('total_harga') }}" placeholder="Enter total harga" readonly>

                                <span class="d-block text-danger" id="error_total_harga"></span>
                            </div>

                            <!-- Section 6: Informasi Tambahan -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>Informasi Tambahan</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="keterangan" class="form-label">Keterangan</label>
                                <input type="text" name="keterangan" id="keterangan" class="form-control"
                                       value="{{ old('keterangan') }}" placeholder="Enter keterangan">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi ke field "Deskripsi" di tab Informasi SIAP BMD</small>
                                <span class="d-block text-danger" id="error_keterangan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-5">
                                <label for="electricity" class="form-label text-dark">Kelistrikan</label>
                                <input type="text" name="electricity" id="electricity" class="form-control"
                                       placeholder="Masukkan Kelistrikan">
                                <span class="d-block text-danger" id="error_electricity"></span>
                            </div>

                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-tags me-2"></i>Detail Spesifik Asset</h5>
                                <div id="accordion">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab 2: Informasi ASPAK -->
                    <div class="tab-pane fade" id="info-aspak" role="tabpanel" aria-labelledby="info-aspak-tab">
                        <!-- Preview Section -->
                        <div class="card mb-4 mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Preview Data Aset</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Tanggal Pembayaran:</small>
                                        <div class="fw-medium" id="preview-tanggal-pembayaran-aspak">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Kode Barang:</small>
                                        <div class="fw-medium" id="preview-kode-barang-aspak">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Nama Barang:</small>
                                        <div class="fw-medium" id="preview-nama-barang-aspak">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Nama Barang di Kontrak:</small>
                                        <div class="fw-medium" id="preview-nama-barang-kontrak-aspak">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Jumlah Barang:</small>
                                        <div class="fw-medium" id="preview-jumlah-barang-aspak">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Keterangan:</small>
                                        <div class="fw-medium" id="preview-keterangan-aspak">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Accordion Preview Section -->
                        <div class="card mb-4 mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Preview Detail Spesifik Asset</h6>
                            </div>
                            <div class="card-body">
                                <div id="accordion-preview-aspak">
                                    <!-- Preview content will be populated by JavaScript -->
                                    <div class="text-center text-muted py-3">
                                        Detail spesifik asset akan muncul di sini setelah jumlah barang ditentukan
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <!-- Section 1: Identifikasi Alat -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-barcode me-2"></i>Identifikasi Alat</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_tool_name" class="form-label">Nama Alat (Nomenklatur)</label>
                                <select name="aspak_tool_name" id="aspak_tool_name" class="form-select">
                                    <option value="">-- Pilih Nama Alat --</option>
                                </select>
                                <span class="d-block text-danger" id="error_aspak_tool_name"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_room" class="form-label">Lokasi</label>
                                <select name="aspak_room" id="aspak_room" class="form-select aspak-room w-100">
                                    <option value="">-- Pilih Lokasi --</option>
                                </select>
                                <span class="d-block text-danger" id="error_aspak_room"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_no_seri" class="form-label">No Seri</label>
                                <input type="text" name="aspak_no_seri" id="aspak_no_seri" class="form-control"
                                       value="{{ old('aspak_no_seri') }}" placeholder="Masukkan No Seri">
                                <span class="d-block text-danger" id="error_aspak_no_seri"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_merk_alat" class="form-label">Merk Alat</label>
                                <input type="text" name="aspak_merk_alat" id="aspak_merk_alat" class="form-control"
                                       value="{{ old('aspak_merk_alat') }}" placeholder="Masukkan Merk Alat">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field
                                    "Merk" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_merk_alat"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_merk_tipe" class="form-label">Merk Tipe (KFA)</label>
                                <input type="text" name="aspak_merk_tipe" id="aspak_merk_tipe" class="form-control"
                                       value="{{ old('aspak_merk_tipe') }}" placeholder="Masukkan Merk Tipe">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Tipe/Model" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_merk_tipe"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_alat" class="form-label">Alat</label>
                                <input type="text" name="aspak_alat" id="aspak_alat" class="form-control"
                                       value="{{ old('aspak_alat') }}" placeholder="Masukkan Alat">
                                <span class="d-block text-danger" id="error_aspak_alat"></span>
                            </div>

                            <!-- Section 2: Kondisi dan Pemeliharaan -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-tools me-2"></i>Kondisi dan Pemeliharaan</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_kondisi_alat" class="form-label">Kondisi Alat</label>
                                <select name="aspak_kondisi_alat" id="aspak_kondisi_alat" class="form-select">
                                    <option value="">-- Pilih Kondisi Alat --</option>
                                    <option value="Baik">Baik</option>
                                    <option value="Rusak Ringan">Rusak Ringan</option>
                                    <option value="Rusak Berat">Rusak Berat</option>
                                </select>
                                <span class="d-block text-danger" id="error_aspak_kondisi_alat"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_distributor_aspak" class="form-label">Distributor</label>
                                <input type="text" name="aspak_distributor" id="aspak_distributor_aspak"
                                       class="form-control" value="{{ old('aspak_distributor') }}"
                                       placeholder="Masukkan Distributor">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Distributor" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_distributor"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_pj_pemeliharaan" class="form-label">PJ Pemeliharaan</label>
                                <input type="text" name="aspak_pj_pemeliharaan" id="aspak_pj_pemeliharaan"
                                       class="form-control" value="{{ old('aspak_pj_pemeliharaan') ?? 'IPFRS/UPAK' }}"
                                       placeholder="Masukkan PJ Pemeliharaan">
                                <span class="d-block text-danger" id="error_aspak_pj_pemeliharaan"></span>
                            </div>

                            <!-- Section 3: Informasi Pengadaan -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-file-invoice-dollar me-2"></i>Informasi Pengadaan</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_no_akl_akd" class="form-label">No. AKL/AKD</label>
                                <input type="text" name="aspak_no_akl_akd" id="aspak_no_akl_akd" class="form-control"
                                       value="{{ old('aspak_no_akl_akd') }}" placeholder="Masukkan No. AKL/AKD">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field
                                    "No. AKD/AKL" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_no_akl_akd"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_produk" class="form-label">Produk</label>
                                <select name="aspak_produk" id="aspak_produk" class="form-select">
                                    <option value="">-- Pilih Produk --</option>
                                    <option value="Dalam Negeri">Dalam Negeri</option>
                                    <option value="Luar Negeri">Luar Negeri</option>
                                </select>
                                <span class="d-block text-danger" id="error_aspak_produk"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_tahun_pengadaan" class="form-label">Tahun Pengadaan</label>
                                <input name="aspak_tahun_pengadaan" id="aspak_tahun_pengadaan"
                                       class="form-control" value="{{ old('aspak_tahun_pengadaan') }}">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field
                                    "Tanggal Pembayaran" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_tahun_pengadaan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_sumber_pengadaan" class="form-label">Sumber Pengadaan</label>
                                <select name="aspak_sumber_pengadaan" id="aspak_sumber_pengadaan"
                                        class="form-select sumber-pengadaan">
                                    <option value="">-- Pilih Sumber Pengadaan --</option>
                                    <option value="apbn">APBN</option>
                                    <option value="apbd">APBD</option>
                                    <option value="hibah">Hibah/Pemberian</option>
                                    <option value="kso">KSO/Pinjam</option>
                                    <option value="blu">BLU/BLUD</option>
                                    <option value="jkn">JKN</option>
                                    <option value="dak">DAK</option>
                                    <option value="sisoin">SISOIN</option>
                                    <option value="swasta">Swasta/Swadana</option>
                                </select>
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field
                                    "Asal Perolehan" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_sumber_pengadaan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_harga_perolehan" class="form-label">Harga Perolehan</label>
                                <input type="text" name="aspak_harga_perolehan" id="aspak_harga_perolehan"
                                       class="form-control" value="{{ old('aspak_harga_perolehan') }}"
                                       placeholder="Masukkan Harga Perolehan">
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field
                                    "Harga Satuan" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_aspak_harga_perolehan"></span>
                            </div>

                            <!-- Section 4: Spesifikasi Teknis -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-cogs me-2"></i>Spesifikasi Teknis</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_keterangan_aspak" class="form-label">Keterangan</label>
                                <textarea name="aspak_keterangan" id="aspak_keterangan_aspak" class="form-control"
                                          rows="3"
                                          placeholder="Masukkan Keterangan">{{ old('aspak_keterangan') }}</textarea>
                                <span class="d-block text-danger" id="error_aspak_keterangan"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_link_gambar" class="form-label">Link Gambar</label>
                                <input type="url" name="aspak_link_gambar" id="aspak_link_gambar" class="form-control"
                                       value="{{ old('aspak_link_gambar') }}" placeholder="Masukkan Link Gambar">
                                <span class="d-block text-danger" id="error_aspak_link_gambar"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_sumber_daya" class="form-label">Sumber Daya</label>
                                <select name="aspak_sumber_daya" id="aspak_sumber_daya" class="form-select">
                                    <option value="">-- Pilih Sumber Daya --</option>
                                    <option value="AC">AC</option>
                                    <option value="DC">DC</option>
                                    <option value="Tanpa Listrik">Tanpa Listrik</option>
                                </select>
                                <span class="d-block text-danger" id="error_aspak_sumber_daya"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_daya" class="form-label">Daya (Watt)</label>
                                <input type="number" name="aspak_daya" id="aspak_daya" class="form-control"
                                       value="{{ old('aspak_daya') }}" placeholder="Masukkan Daya dalam Watt" min="0">
                                <span class="d-block text-danger" id="error_aspak_daya"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_tersambung_ups" class="form-label">Tersambung ke UPS</label>
                                <div class="form-check">
                                    <input type="checkbox" name="aspak_tersambung_ups" id="aspak_tersambung_ups"
                                           class="form-check-input" value="true">
                                    <label class="form-check-label" for="aspak_tersambung_ups">Ya</label>
                                </div>
                                <span class="d-block text-danger" id="error_aspak_tersambung_ups"></span>
                            </div>

                            <!-- Section 5: Informasi Tambahan -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-plus-circle me-2"></i>Informasi Tambahan</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_info_tkdn" class="form-label">Info TKDN</label>
                                <input type="text" name="aspak_info_tkdn" id="aspak_info_tkdn" class="form-control"
                                       value="{{ old('aspak_info_tkdn') }}" placeholder="Masukkan Info TKDN">
                                <span class="d-block text-danger" id="error_aspak_info_tkdn"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_info_kelas_risiko" class="form-label">Info Kelas Risiko</label>
                                <input type="text" name="aspak_info_kelas_risiko" id="aspak_info_kelas_risiko"
                                       class="form-control" value="{{ old('aspak_info_kelas_risiko') }}"
                                       placeholder="Masukkan Info Kelas Risiko">
                                <span class="d-block text-danger" id="error_aspak_info_kelas_risiko"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="aspak_info_umur_teknis" class="form-label">Info Umur Teknis (Tahun)</label>
                                <input type="number" name="aspak_info_umur_teknis" id="aspak_info_umur_teknis"
                                       class="form-control" value="{{ old('aspak_info_umur_teknis') }}"
                                       placeholder="Masukkan Umur Teknis" min="0">
                                <span class="d-block text-danger" id="error_aspak_info_umur_teknis"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Tab 3: Informasi SIAP BMD -->
                    <div class="tab-pane fade" id="info-siap-bmd" role="tabpanel" aria-labelledby="info-siap-bmd-tab">
                        <!-- Preview Section -->
                        <div class="card mb-4 mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Preview Data Aset</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Tanggal Pembayaran:</small>
                                        <div class="fw-medium" id="preview-siap-bmd-tanggal-pembayaran">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Kode Barang:</small>
                                        <div class="fw-medium" id="preview-siap-bmd-kode-barang">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Nama Barang:</small>
                                        <div class="fw-medium" id="preview-siap-bmd-nama-barang">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Nama Barang di Kontrak:</small>
                                        <div class="fw-medium" id="preview-siap-bmd-nama-barang-kontrak">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Jumlah Barang:</small>
                                        <div class="fw-medium" id="preview-siap-bmd-jumlah-barang">-</div>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <small class="text-muted">Keterangan:</small>
                                        <div class="fw-medium" id="preview-siap-bmd-keterangan">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Accordion Preview Section -->
                        <div class="card mb-4 mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-eye me-2"></i>Preview Detail Spesifik Asset</h6>
                            </div>
                            <div class="card-body">
                                <div id="accordion-preview-siap-bmd">
                                    <!-- Preview content will be populated by JavaScript -->
                                    <div class="text-center text-muted py-3">
                                        Detail spesifik asset akan muncul di sini setelah jumlah barang ditentukan
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <!-- Section 1: Administrative Information -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-briefcase me-2"></i>Informasi Administratif</h5>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="siap_bmd_tahun" class="form-label">Tahun</label>
                                <input type="text" name="siap_bmd_tahun" id="siap_bmd_tahun" class="form-control"
                                       readonly>
                                <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field
                                    "Tanggal Pembayaran" di tab Informasi Umum</small>
                                <span class="d-block text-danger" id="error_siap_bmd_tahun"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="siap_bmd_kode_rkbmd" class="form-label">Kode RKBMD</label>
                                <input type="text" name="siap_bmd_kode_rkbmd" id="siap_bmd_kode_rkbmd"
                                       class="form-control">
                                <span class="d-block text-danger" id="error_siap_bmd_kode_rkbmd"></span>
                            </div>

                            <div class="form-group col-md-4 mb-3">
                                <label for="siap_bmd_kegiatan_select" class="form-label">Kegiatan</label>
                                <input type="hidden" name="siap_bmd_kegiatan" id="siap_bmd_kegiatan"
                                       value="{{ old('siap_bmd_kegiatan') }}">
                                <select id="siap_bmd_kegiatan_select" class="form-control" style="width: 100%"></select>
                                <span class="d-block text-danger" id="error_siap_bmd_kegiatan"></span>
                            </div>

                            <div class="form-group col-md-8 mb-3">
                                <label for="siap_bmd_sub_kegiatan_select" class="form-label">Sub Kegiatan</label>
                                <input type="hidden" name="siap_bmd_sub_kegiatan" id="siap_bmd_sub_kegiatan"
                                       value="{{ old('siap_bmd_sub_kegiatan') }}">
                                <select id="siap_bmd_sub_kegiatan_select" class="form-control" style="width: 100%"
                                        disabled></select>
                                <span class="d-block text-danger" id="error_siap_bmd_sub_kegiatan"></span>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Section 2: User/Unit Information -->
                            <div class="col-md-12">
                                <h5 class="mb-3"><i class="fas fa-users me-2"></i>Informasi Pengguna/Unit</h5>
                            </div>

                            <div class="form-group col-md-6 mb-3">
                                <label for="siap_bmd_nama_pengguna_barang" class="form-label">Nama Kusa/Pengguna
                                    Barang</label>
                                <input type="text" name="siap_bmd_nama_pengguna_barang"
                                       id="siap_bmd_nama_pengguna_barang" class="form-control"
                                       placeholder="Masukkan Nama Pengguna Barang">
                                <span class="d-block text-danger" id="error_siap_bmd_nama_pengguna_barang"></span>
                            </div>

                            <div class="form-group col-md-6 mb-3">
                                <label for="siap_bmd_nama_unit" class="form-label">Nama Unit</label>
                                <input type="text" name="siap_bmd_nama_unit" id="siap_bmd_nama_unit"
                                       class="form-control" placeholder="Masukkan Nama Unit" value="{{ $hospitalName ?? '' }}">
                                <span class="d-block text-danger" id="error_siap_bmd_nama_unit"></span>
                            </div>

                            <div class="row">
                                <!-- Section 3: Asset Information (Duplicates from Data Umum) -->
                                <div class="col-md-12">
                                    <h5 class="mb-3"><i class="fas fa-archive me-2"></i>Informasi Aset</h5>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_keterangan" class="form-label">Keterangan</label>
                                    <input type="text" name="siap_bmd_keterangan" id="siap_bmd_keterangan"
                                           class="form-control" readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari field "Uraian Pekerjaan" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_keterangan"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_kode_barang" class="form-label">Kode Barang</label>
                                    <input type="text" name="siap_bmd_kode_barang" id="siap_bmd_kode_barang"
                                           class="form-control" readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Kode Barang" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_kode_barang"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_nama_aset" class="form-label">Nama Aset</label>
                                    <input type="text" name="siap_bmd_nama_aset" id="siap_bmd_nama_aset"
                                           class="form-control" readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Nama Barang" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_nama_aset"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_nama_umum" class="form-label">Nama Umum</label>
                                    <input type="text" name="siap_bmd_nama_umum" id="siap_bmd_nama_umum"
                                           class="form-control" readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Nama Umum" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_nama_umum"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_model" class="form-label">Model</label>
                                    <input type="text" name="siap_bmd_model" id="siap_bmd_model" class="form-control"
                                           readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Tipe/Model" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_model"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_spesifikasi" class="form-label">Spesifikasi</label>
                                    <input type="text" name="siap_bmd_spesifikasi" id="siap_bmd_spesifikasi"
                                           class="form-control" readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Spesifikasi Umum" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_spesifikasi"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_jumlah" class="form-label">Jumlah</label>
                                    <input type="text" name="siap_bmd_jumlah" id="siap_bmd_jumlah" class="form-control"
                                           readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Jumlah Barang" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_jumlah"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_harga" class="form-label">Harga</label>
                                    <input type="text" name="siap_bmd_harga" id="siap_bmd_harga" class="form-control"
                                           readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Harga Satuan" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_harga"></span>
                                </div>

                                <div class="form-group col-md-4 mb-3">
                                    <label for="siap_bmd_deskripsi" class="form-label">Deskripsi</label>
                                    <input type="text" name="siap_bmd_deskripsi" id="siap_bmd_deskripsi"
                                           class="form-control" readonly>
                                    <small class="text-muted"><i class="fas fa-link me-1"></i>Data disinkronisasi dari
                                        field "Keterangan" di tab Informasi Umum</small>
                                    <span class="d-block text-danger" id="error_siap_bmd_deskripsi"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-group col-md-12 mt-3">
                        <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>
                            Simpan
                        </button>
                        <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i>
                            Simpan & Tambah
                        </button>
                        <a href="{{ route('asset-management.asset-hospital.index') }}" class="btn btn-outline-danger"><i
                                class="fas fa-undo me-1"></i>Kembali</a>
                    </div>
            </form>
        </div>
    </div>
@endsection

@push("script")
    <script>
        window.hospitalName = "{{ $hospitalName ?? 'RSUD Dr. Soedarso' }}";
        window.rkbmdPrefix = "{{ config('app.rkbmd_prefix') }}";
    </script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/asset-management/createAssetHospital.js') }}"></script>
@endpush
