@extends('layouts.app')

@section('title', 'Edit Data Asset')

@push('css')
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Tab Styles */
    .nav-tabs .nav-link {
        border: 1px solid #dee2e6;
        border-bottom: none;
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 500;
        border-top-left-radius: 0.375rem;
        border-top-right-radius: 0.375rem;
    }

    .nav-tabs .nav-link.active {
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
        color: #495057;
    }

    .nav-tabs .nav-link:hover {
        border-color: #e9ecef #e9ecef #dee2e6;
        background-color: #e9ecef;
        isolation: isolate;
    }

    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        padding: 1.5rem;
        background-color: #fff;
    }

    /* Preview Section Styles */
    .preview-section {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .preview-section h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.75rem;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.5rem;
    }

    .preview-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.25rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .preview-item:last-child {
        border-bottom: none;
    }

    .preview-label {
        font-weight: 500;
        color: #6c757d;
        min-width: 150px;
    }

    .preview-value {
        color: #495057;
        font-weight: 400;
        text-align: right;
        flex: 1;
    }

    /* Accordion Preview Styles */
    .accordion-preview .accordion-item {
        border: 1px solid #dee2e6;
        margin-bottom: 0.5rem;
    }

    .accordion-preview .accordion-header {
        background-color: #f8f9fa;
    }

    .accordion-preview .accordion-button {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 500;
        padding: 0.75rem 1rem;
        pointer-events: none;
    }

    .accordion-preview .accordion-button:not(.collapsed) {
        background-color: #e9ecef;
        color: #495057;
        box-shadow: none;
    }

    .accordion-preview .accordion-body {
        padding: 1rem;
        background-color: #fff;
    }

    .accordion-preview {
        opacity: 0.8;
    }

    /* Form Section Titles */
    .form-section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #dee2e6;
    }

    .tab-placeholder {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }

    .tab-placeholder h5 {
        color: #495057;
        margin-bottom: 1rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .preview-item {
            flex-direction: column;
            align-items: flex-start;
        }

        .preview-label {
            min-width: auto;
            margin-bottom: 0.25rem;
        }

        .preview-value {
            text-align: left;
        }
    }
</style>
@endpush

@section('content')
<div class="d-flex align-items-center mb-3">
    <div>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="javascript:;">Manajemen Asset</a></li>
            <li class="breadcrumb-item"><a href="{{ route('asset-management.asset-hospital.index') }}">Data Asset</a></li>
            <li class="breadcrumb-item active">Edit Data Asset</li>
        </ol>
        <h1 class="page-header mb-0">Edit Data Asset</h1>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="assetTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="informasi-umum-tab" data-bs-toggle="tab" data-bs-target="#informasi-umum" type="button" role="tab" aria-controls="informasi-umum" aria-selected="true">
                    <i class="fas fa-info-circle me-2"></i>Informasi Umum
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="informasi-aspak-tab" data-bs-toggle="tab" data-bs-target="#informasi-aspak" type="button" role="tab" aria-controls="informasi-aspak" aria-selected="false">
                    <i class="fas fa-hospital me-2"></i>Informasi ASPAK
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="informasi-siap-bmd-tab" data-bs-toggle="tab" data-bs-target="#informasi-siap-bmd" type="button" role="tab" aria-controls="informasi-siap-bmd" aria-selected="false">
                    <i class="fas fa-database me-2"></i>Informasi SIAP BMD
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="assetTabsContent">
            <!-- Tab Informasi Umum -->
            <div class="tab-pane fade show active" id="informasi-umum" role="tabpanel" aria-labelledby="informasi-umum-tab">
                <form id="formdata" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="jumlah_barang" name="jumlah_barang" value="{{ $assetEntry->assets->count() }}">
                    @foreach($assetEntry->assets as $asset)
                        <input type="hidden" name="asset_id[]" value="{{ $asset->id }}">
                    @endforeach
                    <!-- Informasi Pengadaan -->
                     <div class="row">
                         <div class="col-12">
                             <h5 class="mb-3"><i class="fas fa-shopping-cart me-2"></i>Informasi Pengadaan</h5>
                         </div>
                     </div>
                     <div class="row">
                         <div class="form-group col-md-4 mb-3">
                <label for="tanggal_barang_masuk" class="form-label">Tanggal Barang Masuk</label>
                <input type="date" name="tanggal_barang_masuk" id="tanggal_barang_masuk" class="form-control" value="{{ $assetEntry->received_date ?? old('tanggal_barang_masuk') }}">

                <span class="d-block text-danger" id="error_tanggal_barang_masuk"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="rekening_belanja" class="form-label">Uraian Rekening Belanja</label>
                <input type="text" name="rekening_belanja" id="rekening_belanja" class="form-control" value="{{ $assetEntry->purchasing_account ?? old('rekening_belanja') }}" placeholder="Enter Uraian rekening belanja">

                <span class="d-block text-danger" id="error_rekening_belanja"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="spending_account_code" class="form-label">Kode Rekening Belanja</label>
                <input type="text" name="spending_account_code" id="spending_account_code" class="form-control" value="{{ $assetEntry->spending_account_code ?? old('spending_account_code') }}" placeholder="Enter kode rekening belanja">
                <span class="d-block text-danger" id="error_spending_account_code"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="tanggal_pembayaran" class="form-label">Tanggal Pembayaran</label>
                <input type="date" name="tanggal_pembayaran" id="tanggal_pembayaran" class="form-control" value="{{ $assetEntry->payment_date ?? old('tanggal_pembayaran') }}">

                <span class="d-block text-danger" id="error_tanggal_pembayaran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_bast_kontrak" class="form-label">No. Kontrak/SP </label>
                <input type="text" name="no_bast_kontrak" id="no_bast_kontrak" class="form-control" value="{{ $assetEntry->bast_contract_number ?? old('no_bast_kontrak') }}" placeholder="Enter No. Kontrak/SP ">

                <span class="d-block text-danger" id="error_no_bast_kontrak"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_bast_pembayaran" class="form-label">No. BA Pembayaran</label>
                <input type="text" name="no_bast_pembayaran" id="no_bast_pembayaran" class="form-control" value="{{ $assetEntry->bast_payment_number ?? old('no_bast_pembayaran') }}" placeholder="Enter No. BA Pembayaran">

                <span class="d-block text-danger" id="error_no_bast_pembayaran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="asal_perolehan" class="form-label">Asal Perolehan</label>
                <select name="asal_perolehan" id="asal_perolehan" class="form-select asal-perolehan">
                    <option {{ $assetEntry->source_supply == "APBN" ? 'selected' : '' }} value="apbn">APBN</option>
                    <option {{ $assetEntry->source_supply == "APBD" ? 'selected' : '' }} value="apbd">APBD</option>
                    <option {{ $assetEntry->source_supply == "DAK" ? 'selected' : '' }} value="dak">DAK</option>
                    <option {{ $assetEntry->source_supply == "BLUD" ? 'selected' : '' }} value="blud">BLUD</option>
                    <option {{ $assetEntry->source_supply == "HIBAH" ? 'selected' : '' }} value="hibah">Hibah</option>
                </select>

                <span class="d-block text-danger" id="error_asal_perolehan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="distributor" class="form-label">Distributor</label>
                <select name="distributor" id="distributor" class="form-select distributor w-100">
                    <option value="">-- Pilih Distributor --</option>
                    <option value="{{ $assetEntry->distributor_id }}" selected>{{ $assetEntry->distributor->distributor_name }}</option>
                </select>

                <span class="d-block text-danger" id="error_distributor"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kategori" class="form-label">Kategori</label>
                <select name="kategori" id="kategori" class="form-select kategori w-100" disabled>
                    <option value="">-- Pilih Kategori --</option>
                    <option value="{{ $assetEntry->category_id }}" selected>{{ $assetEntry->category->category_name }}</option>
                </select>

                <span class="d-block text-danger" id="error_kategori"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="kode_barang" class="form-label">Kode Barang</label>
                <select name="kode_barang" id="kode_barang" class="form-select kode-barang" disabled>
                    <option value="">-- Pilih Kode Barang --</option>
                    <option value="{{ $assetEntry->item_id }}" selected>{{ $assetEntry->item->item_name }}</option>
                </select>

                <span class="d-block text-danger" id="error_kode_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_barang" class="form-label">Nama Barang</label>
                <input type="text" name="nama_barang" id="nama_barang" class="form-control" value="{{ $assetEntry->item->item_name ?? old('nama_barang') }}" placeholder="Enter nama barang" disabled>

                <span class="d-block text-danger" id="error_nama_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="nama_umum" class="form-label">Nama Umum</label>
                <input type="text" name="nama_umum" id="nama_umum" class="form-control" value="{{ $assetEntry->asset_name ?? old('nama_umum') }}" placeholder="Enter nama umum">

                <span class="d-block text-danger" id="error_nama_umum"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="merk" class="form-label">Merk</label>
                <input type="text" name="merk" id="merk" class="form-control" value="{{ $assetEntry->brand ?? old('merk') }}" placeholder="Enter merk">

                <span class="d-block text-danger" id="error_merk"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="tipe" class="form-label">Tipe/Model</label>
                <input type="text" name="tipe" id="tipe" class="form-control" value="{{ $assetEntry->type ?? old('tipe') }}" placeholder="Enter tipe">

                <span class="d-block text-danger" id="error_tipe"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="no_akd" class="form-label">No. AKD/AKL</label>
                <input type="text" name="no_akd" id="no_akd" class="form-control" value="{{ $assetEntry->akd_number ?? old('no_akd') }}" placeholder="Enter no. akd">

                <span class="d-block text-danger" id="error_no_akd"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="spesifikasi_umum" class="form-label">Spesifikasi Umum</label>
                <input type="text" name="spesifikasi_umum" id="spesifikasi_umum" class="form-control" value="{{ $assetEntry->general_specifications ?? old('spesifikasi_umum') }}" placeholder="Enter spesifikasi umum">

                <span class="d-block text-danger" id="error_spesifikasi_umum"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="bahan" class="form-label">Bahan</label>
                <input type="text" name="bahan" id="bahan" class="form-control" value="{{ $assetEntry->material ?? old('bahan') }}" placeholder="Enter bahan">

                <span class="d-block text-danger" id="error_bahan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="ukuran" class="form-label">Ukuran</label>
                <input type="text" name="ukuran" id="ukuran" class="form-control" value="{{ $assetEntry->size ?? old('ukuran') }}" placeholder="Enter ukuran">

                <span class="d-block text-danger" id="error_ukuran"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="satuan_barang" class="form-label">Satuan Barang</label>
                <select name="satuan_barang" id="satuan_barang" class="form-select satuan">
                    <option value="">-- Pilih Satuan Barang --</option>
                    <option value="{{ $assetEntry->uom_id }}" selected>{{ $assetEntry->uom->uom_name }}</option>
                </select>

                <span class="d-block text-danger" id="error_satuan_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="harga_satuan" class="form-label">Harga Satuan</label>
                <input type="text" name="harga_satuan" id="harga_satuan" class="form-control" value="{{ number_format($assetEntry->unit_price, 0, ",", "") ?? old('harga_satuan') }}" placeholder="Enter harga satuan">

                <span class="d-block text-danger" id="error_harga_satuan"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="jumlah_barang" class="form-label">Jumlah Barang</label>
                <input type="number" name="jumlah_barang" id="jumlah_barang" class="form-control" value="{{ $assetEntry->quantity ?? old('jumlah_barang') }}" readonly placeholder="Enter jumlah barang">

                <span class="d-block text-danger" id="error_jumlah_barang"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="total_harga" class="form-label">Total Harga</label>
                <input type="text" name="total_harga" id="total_harga" class="form-control" value="{{ number_format($assetEntry->total_price, 0, ",", "") ?? old('total_harga') }}" placeholder="Enter total harga" readonly>

                <span class="d-block text-danger" id="error_total_harga"></span>
            </div>

            <div class="form-group col-md-4 mb-3">
                <label for="contract_form" class="form-label">Bentuk Kontrak</label>
                <input type="text" name="contract_form" id="contract_form" class="form-control" value="{{ $assetEntry->contract_form ?? old('contract_form') }}" placeholder="Enter bentuk kontrak">
                <span class="d-block text-danger" id="error_contract_form"></span>
            </div>

                     </div>

                     <!-- Informasi Barang -->
                     <div class="row">
                         <div class="col-12">
                             <h5 class="mb-3"><i class="fas fa-box me-2"></i>Informasi Barang</h5>
                         </div>
                     </div>
                     <div class="row">


            {{-- <div class="form-group col-md-4 mb-5">
                <label for="kondisi" class="form-label">Kondisi</label>
                <select name="kondisi" id="kondisi" class="form-select">
                    <option value="">-- Pilih Kondisi --</option>
                    <option {{ $assetEntry->condition == "BAIK" ? 'selected' : '' }} value="Baik">Baik</option>
            <option {{ $assetEntry->condition == "RUSAK_RINGAN" ? 'selected' : '' }} value="Rusak Ringan">Rusak Ringan</option>
            <option {{ $assetEntry->condition == "RUSAK_BERAT" ? 'selected' : '' }} value="Rusak Berat ">Rusak Berat</option>
            </select>

            <span class="d-block text-danger" id="error_kondisi"></span>
    </div> --}}

                         <div class="form-group col-md-4 mb-3">
                             <label for="keterangan" class="form-label">Keterangan</label>
                             <input type="text" name="keterangan" id="keterangan" class="form-control" value="{{ $assetEntry->description ?? old('keterangan') }}" placeholder="Enter keterangan">
                             <span class="d-block text-danger" id="error_keterangan"></span>
                         </div>

                         <div class="form-group col-md-4 mb-3">
                             <label for="kelistrikan" class="form-label">Kelistrikan</label>
                             <input type="text" name="kelistrikan" id="kelistrikan" class="form-control" value="{{ old('kelistrikan') }}" placeholder="Enter kelistrikan">
                             <span class="d-block text-danger" id="error_kelistrikan"></span>
                         </div>
                     </div>

                     <!-- Detail Spesifik Asset -->
                     <div class="row">
                         <div class="col-12">
                             <h5 class="mb-3"><i class="fas fa-list-alt me-2"></i>Detail Spesifik Asset</h5>
                         </div>
                     </div>

    <div class="col-md-12">
        <div class="accordion" id="accordion">
            @foreach($assetEntry->assets as $asset)
            <div class="accordion-item dynamic-accordion">
                <div class="accordion-header" id="heading{{ $loop->iteration }}">
                    <button class="accordion-button bg-cyan-600 text-white px-3 py-10px pointer-cursor" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $loop->iteration }}">
                        <i class="fa fa-circle fa-fw ${color} me-2 fs-8px"></i> Form Input {{ $loop->iteration }}
                    </button>
                </div>
                <div id="collapse{{ $loop->iteration }}" class="accordion-collapse collapse" data-bs-parent="#accordion">
                    <div class="accordion-body text-white row">
                        <div class="form-group col-md-4 mb-3">
                            <label for="register_code_{{ $loop->iteration }}" class="form-label text-dark">Register Code</label>
                            <input type="text" name="register_code[]" id="register_code_{{ $loop->iteration }}" class="form-control" placeholder="Enter register code" value="{{ $asset->register_code }}" disabled>

                            <span class="d-block text-danger" id="error_register_code"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="no_sn_{{ $loop->iteration }}" class="form-label text-dark">No. SN</label>
                            <input type="text" name="no_sn[]" id="no_sn_{{ $loop->iteration }}" class="form-control" placeholder="Enter no. sn" value="{{ $asset->serial_number }}">

                            <span class="d-block text-danger" id="error_no_sn"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="qr_code_{{ $loop->iteration }}" class="form-label text-dark">QR Code</label>
                            <input type="text" name="qr_code[]" id="qr_code_{{ $loop->iteration }}" class="form-control" placeholder="Enter qr code" readonly value="{{ $asset->qr_code }}" disabled>

                            <span class="d-block text-danger" id="error_qr_code"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="tag_rfid_{{ $loop->iteration }}" class="form-label text-dark">Tag RFID</label>
                            <input type="text" name="tag_rfid[]" id="tag_rfid_{{ $loop->iteration }}" class="form-control" placeholder="Enter tag rfid" value="{{ $asset->rfid_tag }}">

                            <span class="d-block text-danger" id="error_tag_rfid"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="no_rangka_{{ $loop->iteration }}" class="form-label text-dark">No. Rangka</label>
                            <input type="text" name="no_rangka[]" id="no_rangka_{{ $loop->iteration }}" class="form-control" placeholder="Enter no rangka" value="{{ $asset->chassis_number }}">

                            <span class="d-block text-danger" id="error_no_rangka"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="no_mesin_{{ $loop->iteration }}" class="form-label text-dark">No. Mesin</label>
                            <input type="text" name="no_mesin[]" id="no_mesin_{{ $loop->iteration }}" class="form-control" placeholder="Enter no mesin" value="{{ $asset->engine_number }}">

                            <span class="d-block text-danger" id="error_no_mesin"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="no_polisi_{{ $loop->iteration }}" class="form-label text-dark">No. Polisi</label>
                            <input type="text" name="no_polisi[]" id="no_polisi_{{ $loop->iteration }}" class="form-control" placeholder="Enter no polisi" value="{{ $asset->license_plate_number }}">

                            <span class="d-block text-danger" id="error_no_polisi"></span>
                        </div>
                        <div class="form-group col-md-4 mb-3">
                            <label for="no_bpkb_{{ $loop->iteration }}" class="form-label text-dark">No. BPKB</label>
                            <input type="text" name="no_bpkb[]" id="no_bpkb_{{ $loop->iteration }}" class="form-control" placeholder="Enter no bpkb" value="{{ $asset->bpkb_number }}">

                            <span class="d-block text-danger" id="error_no_bpkb"></span>
                        </div>

                        <div class="form-group col-md-4 mb-3">
                            <label for="kondisi_{{ $loop->iteration }}" class="form-label text-dark">Kondisi</label>
                            <select name="kondisi[]" id="kondisi_{{ $loop->iteration }}" class="form-select">
                                <option value="">-- Pilih Kondisi --</option>
                                <option value="BAIK" {{ $asset->asset_condition == "BAIK" ? 'selected' : '' }}>Baik</option>
                                <option value="RUSAK_RINGAN" {{ $asset->asset_condition == "RUSAK_RINGAN" ? 'selected' : '' }}>Rusak Ringan</option>
                                <option value="RUSAK_BERAT" {{ $asset->asset_condition == "RUSAK_BERAT" ? 'selected' : '' }}>Rusak Berat</option>
                            </select>

                            <span class="d-block text-danger" id="error_kondisi"></span>
                        </div>

                        <div class="form-group col-md-4 mb-3">
                            <label for="sub_activity_code_{{ $loop->iteration }}" class="form-label text-dark">Kode Sub Kegiatan</label>
                            <input type="text" name="sub_activity_code[]" id="sub_activity_code_{{ $loop->iteration }}" class="form-control" placeholder="Masukkan Kode Sub Kegiatan" value="{{ $asset->sub_activity_code }}">
                            <span class="d-block text-danger" id="error_sub_activity_code"></span>
                        </div>

                        <div class="form-group col-md-4 mb-3">
                            <label for="sub_activity_name_{{ $loop->iteration }}" class="form-label text-dark">Nama Sub Kegiatan</label>
                            <input type="text" name="sub_activity_name[]" id="sub_activity_name_{{ $loop->iteration }}" class="form-control" placeholder="Masukkan Nama Sub Kegiatan" value="{{ $asset->sub_activity_name }}">
                            <span class="d-block text-danger" id="error_sub_activity_name"></span>
                        </div>

                        <div class="form-group col-md-4 mb-3">
                            <label for="electricity_{{ $loop->iteration }}" class="form-label text-dark">Kelistrikan</label>
                            <input type="text" name="electricity[]" id="electricity_{{ $loop->iteration }}" class="form-control" placeholder="Masukkan Kelistrikan" value="{{ $asset->electricity }}">
                            <span class="d-block text-danger" id="error_electricity"></span>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
                     </div>

                     <div class="form-group col-md-12 mt-3">
                         <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                         <a href="{{ route('asset-management.asset-hospital.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
                     </div>
                 </form>
             </div>

             <!-- Tab Informasi ASPAK -->
             <div class="tab-pane fade" id="informasi-aspak" role="tabpanel" aria-labelledby="informasi-aspak-tab">
                 <!-- Preview Data Asset -->
                 <div class="preview-section">
                     <h6><i class="fas fa-eye me-2"></i>Preview Data Asset</h6>
                     <div class="preview-item">
                         <span class="preview-label">Tanggal Pembayaran:</span>
                         <span class="preview-value" id="preview-tanggal-pembayaran-aspak">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Kode Barang:</span>
                         <span class="preview-value" id="preview-kode-barang-aspak">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Nama Barang:</span>
                         <span class="preview-value" id="preview-nama-barang-aspak">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Jumlah Barang:</span>
                         <span class="preview-value" id="preview-jumlah-barang-aspak">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Keterangan:</span>
                         <span class="preview-value" id="preview-keterangan-aspak">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Satuan Barang:</span>
                         <span class="preview-value" id="preview-satuan-barang-aspak">-</span>
                     </div>
                 </div>

                 <!-- Detail Spesifik Asset -->
                 <div class="preview-section accordion-preview">
                     <h6><i class="fas fa-list-alt me-2"></i>Detail Spesifik Asset</h6>
                     <div class="accordion" id="accordionPreviewAspak">
                         <!-- Placeholder accordion items will be generated by JavaScript -->
                     </div>
                 </div>

                 <!-- Form Fields ASPAK -->
                 <div class="row">
                     <div class="col-12">
                         <h5 class="mb-3"><i class="fas fa-hospital me-2"></i>Identifikasi Alat</h5>
                     </div>
                 </div>
                 <div class="row">
                     <div class="form-group col-md-4 mb-3">
                         <label for="nama_alat_aspak" class="form-label">Nama Alat</label>
                         <input type="text" name="nama_alat_aspak" id="nama_alat_aspak" class="form-control" placeholder="Enter nama alat">
                         <span class="d-block text-danger" id="error_nama_alat_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="lokasi_aspak" class="form-label">Lokasi</label>
                         <select name="lokasi_aspak" id="lokasi_aspak" class="form-select ruangan-aspak w-100">
                             <option value="">-- Pilih Lokasi --</option>
                         </select>
                         <span class="d-block text-danger" id="error_lokasi_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="no_seri_aspak" class="form-label">No Seri</label>
                         <input type="text" name="no_seri_aspak" id="no_seri_aspak" class="form-control" placeholder="Enter no seri">
                         <span class="d-block text-danger" id="error_no_seri_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="merk_alat_aspak" class="form-label">Merk Alat</label>
                         <input type="text" name="merk_alat_aspak" id="merk_alat_aspak" class="form-control" placeholder="Enter merk alat">
                         <span class="d-block text-danger" id="error_merk_alat_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="merk_tipe_aspak" class="form-label">Merk Tipe</label>
                         <input type="text" name="merk_tipe_aspak" id="merk_tipe_aspak" class="form-control" placeholder="Enter merk tipe">
                         <span class="d-block text-danger" id="error_merk_tipe_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="alat_aspak" class="form-label">Alat</label>
                         <select name="alat_aspak" id="alat_aspak" class="form-select alat-aspak w-100">
                             <option value="">-- Pilih Alat --</option>
                         </select>
                         <span class="d-block text-danger" id="error_alat_aspak"></span>
                     </div>
                 </div>

                 <!-- Kondisi dan Pemeliharaan -->
                 <div class="row">
                     <div class="col-12">
                         <h5 class="mb-3"><i class="fas fa-tools me-2"></i>Kondisi dan Pemeliharaan</h5>
                     </div>
                 </div>
                 <div class="row">
                     <div class="form-group col-md-4 mb-3">
                         <label for="kondisi_alat_aspak" class="form-label">Kondisi Alat</label>
                         <select name="kondisi_alat_aspak" id="kondisi_alat_aspak" class="form-select">
                             <option value="">-- Pilih Kondisi --</option>
                             <option value="BAIK">Baik</option>
                             <option value="RUSAK_RINGAN">Rusak Ringan</option>
                             <option value="RUSAK_BERAT">Rusak Berat</option>
                         </select>
                         <span class="d-block text-danger" id="error_kondisi_alat_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="distributor_aspak" class="form-label">Distributor</label>
                         <input type="text" name="distributor_aspak" id="distributor_aspak" class="form-control" placeholder="Enter distributor">
                         <span class="d-block text-danger" id="error_distributor_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="pj_pemeliharaan_aspak" class="form-label">PJ Pemeliharaan</label>
                         <input type="text" name="pj_pemeliharaan_aspak" id="pj_pemeliharaan_aspak" class="form-control" placeholder="Enter PJ pemeliharaan">
                         <span class="d-block text-danger" id="error_pj_pemeliharaan_aspak"></span>
                     </div>
                 </div>

                 <!-- Informasi Pengadaan -->
                 <div class="row">
                     <div class="col-12">
                         <h5 class="mb-3"><i class="fas fa-shopping-cart me-2"></i>Informasi Pengadaan</h5>
                     </div>
                 </div>
                 <div class="row">
                     <div class="form-group col-md-4 mb-3">
                         <label for="no_akl_akd_aspak" class="form-label">No. AKL/AKD</label>
                         <input type="text" name="no_akl_akd_aspak" id="no_akl_akd_aspak" class="form-control" placeholder="Enter no. AKL/AKD">
                         <span class="d-block text-danger" id="error_no_akl_akd_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="produk_aspak" class="form-label">Produk</label>
                         <input type="text" name="produk_aspak" id="produk_aspak" class="form-control" placeholder="Enter produk">
                         <span class="d-block text-danger" id="error_produk_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="tahun_pengadaan_aspak" class="form-label">Tahun Pengadaan</label>
                         <input type="text" name="tahun_pengadaan_aspak" id="tahun_pengadaan_aspak" class="form-control" placeholder="Enter tahun pengadaan">
                         <span class="d-block text-danger" id="error_tahun_pengadaan_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="sumber_pengadaan_aspak" class="form-label">Sumber Pengadaan</label>
                         <input type="text" name="sumber_pengadaan_aspak" id="sumber_pengadaan_aspak" class="form-control" placeholder="Enter sumber pengadaan">
                         <span class="d-block text-danger" id="error_sumber_pengadaan_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="harga_perolehan_aspak" class="form-label">Harga Perolehan</label>
                         <input type="text" name="harga_perolehan_aspak" id="harga_perolehan_aspak" class="form-control" placeholder="Enter harga perolehan">
                         <span class="d-block text-danger" id="error_harga_perolehan_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="keterangan_aspak" class="form-label">Keterangan</label>
                         <input type="text" name="keterangan_aspak" id="keterangan_aspak" class="form-control" placeholder="Enter keterangan">
                         <span class="d-block text-danger" id="error_keterangan_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="link_gambar_aspak" class="form-label">Link Gambar</label>
                         <input type="url" name="link_gambar_aspak" id="link_gambar_aspak" class="form-control" placeholder="Enter link gambar">
                         <span class="d-block text-danger" id="error_link_gambar_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="sumber_daya_aspak" class="form-label">Sumber Daya</label>
                         <input type="text" name="sumber_daya_aspak" id="sumber_daya_aspak" class="form-control" placeholder="Enter sumber daya">
                         <span class="d-block text-danger" id="error_sumber_daya_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="daya_watt_aspak" class="form-label">Daya (Watt)</label>
                         <input type="number" name="daya_watt_aspak" id="daya_watt_aspak" class="form-control" placeholder="Enter daya dalam watt">
                         <span class="d-block text-danger" id="error_daya_watt_aspak"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="tersambung_ups_aspak" class="form-label">Tersambung ke UPS</label>
                         <select name="tersambung_ups_aspak" id="tersambung_ups_aspak" class="form-select">
                             <option value="">-- Pilih --</option>
                             <option value="1">Ya</option>
                             <option value="0">Tidak</option>
                         </select>
                         <span class="d-block text-danger" id="error_tersambung_ups_aspak"></span>
                     </div>
                 </div>
             </div>

             <!-- Tab Informasi SIAP BMD -->
             <div class="tab-pane fade" id="informasi-siap-bmd" role="tabpanel" aria-labelledby="informasi-siap-bmd-tab">
                 <!-- Preview Data Asset -->
                 <div class="preview-section">
                     <h6><i class="fas fa-eye me-2"></i>Preview Data Asset</h6>
                     <div class="preview-item">
                         <span class="preview-label">Tanggal Pembayaran:</span>
                         <span class="preview-value" id="preview-tanggal-pembayaran-siap-bmd">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Kode Barang:</span>
                         <span class="preview-value" id="preview-kode-barang-siap-bmd">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Nama Barang:</span>
                         <span class="preview-value" id="preview-nama-barang-siap-bmd">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Jumlah Barang:</span>
                         <span class="preview-value" id="preview-jumlah-barang-siap-bmd">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Keterangan:</span>
                         <span class="preview-value" id="preview-keterangan-siap-bmd">-</span>
                     </div>
                     <div class="preview-item">
                         <span class="preview-label">Satuan Barang:</span>
                         <span class="preview-value" id="preview-satuan-barang-siap-bmd">-</span>
                     </div>
                 </div>

                 <!-- Detail Spesifik Asset -->
                 <div class="preview-section accordion-preview">
                     <h6><i class="fas fa-list-alt me-2"></i>Detail Spesifik Asset</h6>
                     <div class="accordion" id="accordionPreviewSiapBmd">
                         <!-- Placeholder accordion items will be generated by JavaScript -->
                     </div>
                 </div>

                 <!-- Form Fields SIAP BMD -->
                 <div class="row">
                     <div class="col-12">
                         <h5 class="mb-3"><i class="fas fa-clipboard-list me-2"></i>Informasi Administratif</h5>
                     </div>
                 </div>
                 <div class="row">
                     <div class="form-group col-md-4 mb-3">
                         <label for="tahun_siap_bmd" class="form-label">Tahun</label>
                         <input type="text" name="tahun_siap_bmd" id="tahun_siap_bmd" class="form-control" placeholder="Enter tahun">
                         <span class="d-block text-danger" id="error_tahun_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="kode_rkbmd_siap_bmd" class="form-label">Kode RKBMD</label>
                         <input type="text" name="kode_rkbmd_siap_bmd" id="kode_rkbmd_siap_bmd" class="form-control" placeholder="Enter kode RKBMD">
                         <span class="d-block text-danger" id="error_kode_rkbmd_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="kegiatan_siap_bmd" class="form-label">Kegiatan</label>
                         <select name="kegiatan_siap_bmd" id="kegiatan_siap_bmd" class="form-select kegiatan-siap-bmd w-100">
                             <option value="">-- Pilih Kegiatan --</option>
                         </select>
                         <span class="d-block text-danger" id="error_kegiatan_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="sub_kegiatan_siap_bmd" class="form-label">Sub Kegiatan</label>
                         <select name="sub_kegiatan_siap_bmd" id="sub_kegiatan_siap_bmd" class="form-select sub-kegiatan-siap-bmd w-100">
                             <option value="">-- Pilih Sub Kegiatan --</option>
                         </select>
                         <span class="d-block text-danger" id="error_sub_kegiatan_siap_bmd"></span>
                     </div>
                 </div>

                 <!-- Informasi Pengguna/Unit -->
                 <div class="row">
                     <div class="col-12">
                         <h5 class="mb-3"><i class="fas fa-users me-2"></i>Informasi Pengguna/Unit</h5>
                     </div>
                 </div>
                 <div class="row">
                     <div class="form-group col-md-6 mb-3">
                         <label for="nama_pengguna_barang_siap_bmd" class="form-label">Nama Pengguna Barang</label>
                         <input type="text" name="nama_pengguna_barang_siap_bmd" id="nama_pengguna_barang_siap_bmd" class="form-control" placeholder="Enter nama pengguna barang">
                         <span class="d-block text-danger" id="error_nama_pengguna_barang_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-6 mb-3">
                         <label for="nama_unit_siap_bmd" class="form-label">Nama Unit</label>
                         <input type="text" name="nama_unit_siap_bmd" id="nama_unit_siap_bmd" class="form-control" placeholder="Enter nama unit">
                         <span class="d-block text-danger" id="error_nama_unit_siap_bmd"></span>
                     </div>
                 </div>

                 <!-- Informasi Aset -->
                 <div class="row">
                     <div class="col-12">
                         <h5 class="mb-3"><i class="fas fa-box me-2"></i>Informasi Aset</h5>
                         <p class="text-muted">Data berikut akan disinkronkan dari tab Informasi Umum</p>
                     </div>
                 </div>
                 <div class="row">
                     <div class="form-group col-md-4 mb-3">
                         <label for="keterangan_siap_bmd" class="form-label">Keterangan</label>
                         <input type="text" name="keterangan_siap_bmd" id="keterangan_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_keterangan_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="kode_barang_siap_bmd" class="form-label">Kode Barang</label>
                         <input type="text" name="kode_barang_siap_bmd" id="kode_barang_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_kode_barang_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="nama_aset_siap_bmd" class="form-label">Nama Aset</label>
                         <input type="text" name="nama_aset_siap_bmd" id="nama_aset_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_nama_aset_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="nama_umum_siap_bmd" class="form-label">Nama Umum</label>
                         <input type="text" name="nama_umum_siap_bmd" id="nama_umum_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_nama_umum_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="model_siap_bmd" class="form-label">Model</label>
                         <input type="text" name="model_siap_bmd" id="model_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_model_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="spesifikasi_siap_bmd" class="form-label">Spesifikasi</label>
                         <input type="text" name="spesifikasi_siap_bmd" id="spesifikasi_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_spesifikasi_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="jumlah_siap_bmd" class="form-label">Jumlah</label>
                         <input type="text" name="jumlah_siap_bmd" id="jumlah_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_jumlah_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="harga_siap_bmd" class="form-label">Harga</label>
                         <input type="text" name="harga_siap_bmd" id="harga_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_harga_siap_bmd"></span>
                     </div>
                     <div class="form-group col-md-4 mb-3">
                         <label for="deskripsi_siap_bmd" class="form-label">Deskripsi</label>
                         <input type="text" name="deskripsi_siap_bmd" id="deskripsi_siap_bmd" class="form-control" placeholder="Akan disinkronkan dari Informasi Umum" readonly>
                         <span class="d-block text-danger" id="error_deskripsi_siap_bmd"></span>
                     </div>
                 </div>
             </div>
         </div>

         <!-- Form Actions -->
         <div class="row mt-4">
             <div class="col-12">
                 <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                 <a href="{{ route('asset-management.asset-hospital.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
             </div>
         </div>
     </div>
 </div>

 <!-- Hidden Inputs -->
 <input type="hidden" id="jumlah_barang" value="{{ $assetEntry->assets->count() }}">

@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script>
    // Window variables for JavaScript
    window.hospitalName = '{{ $assetEntry->hospital->name ?? "" }}';
    window.rkbmdPrefix = '{{ $assetEntry->hospital->rkbmd_prefix ?? "" }}';
</script>
<script src="{{ asset('/js/app/asset-management/editAssetHospital.js') }}"></script>
<script>
    let assetEntryid = "{{ $assetEntry->id }}";
</script>
@endpush