alter table assets
    add default_image text null after latest_balance;


CREATE TABLE `room_subs`
(
    `id`              bigint(20)   NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `room_id`         bigint       NULL,
    `sub_room_code`   varchar(64)  NULL,
    `sub_room_name`   varchar(128) NULL,
    `device_id`       varchar(128) NULL,
    `active`          tinyint(1)   NOT NULL DEFAULT '1',
    `created_at`      timestamp    NULL,
    `updated_at`      timestamp    NULL,
    `created_by`      bigint(20)   NULL,
    `updated_by`      bigint(20)   NULL,
    `created_by_name` varchar(128) NULL,
    `updated_by_name` varchar(128) NULL,
    FOREIGN KEY (`room_id`) REFERENCES `rooms` (`id`)
);

-- =========================================================
-- TABEL: aspak_service_rooms
-- =========================================================
DROP TABLE IF EXISTS aspak_service_rooms;
CREATE TABLE aspak_service_rooms
(
    id                BIGINT UNSIGNED        NOT NULL AUTO_INCREMENT,
    room_service_code VARCHAR(64)            NOT NULL,
    room_service_name VARCHAR(128)           NULL,
    parent_id         BIGINT UNSIGNED        NULL,
    tree              ENUM ('BRANCH','LEAF') NULL,
    -- audit fields
    created_at        DATETIME(3)            NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_at        DATETIME(3)            NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    deleted_at        DATETIME(3)            NULL,
    created_by        BIGINT UNSIGNED        NULL,
    updated_by        BIGINT UNSIGNED        NULL,
    deleted_by        BIGINT UNSIGNED        NULL,
    created_by_name   VARCHAR(128)           NULL,
    updated_by_name   VARCHAR(128)           NULL,
    deleted_by_name   VARCHAR(128)           NULL,

    -- kunci & relasi
    CONSTRAINT pk_asr PRIMARY KEY (id),
    CONSTRAINT uk_asr_code UNIQUE (room_service_code),
    CONSTRAINT fk_asr_parent
        FOREIGN KEY (parent_id) REFERENCES aspak_service_rooms (id)
            ON UPDATE CASCADE ON DELETE SET NULL
)
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci;

-- Index kinerja
CREATE INDEX idx_asr_parent_tree ON aspak_service_rooms (parent_id, tree);
CREATE INDEX idx_asr_deleted_at ON aspak_service_rooms (deleted_at);
-- Prefix search (LIKE 'foo%')
CREATE INDEX idx_asr_name_prefix ON aspak_service_rooms (room_service_name);
-- Fulltext search (LIKE '%foo%') — aktifkan jika dibutuhkan
ALTER TABLE aspak_service_rooms
    ADD FULLTEXT INDEX ftx_asr_name (room_service_name);


-- =========================================================
-- TABEL: aspak_items
-- =========================================================
DROP TABLE IF EXISTS aspak_items;
CREATE TABLE aspak_items
(
    id              BIGINT UNSIGNED        NOT NULL AUTO_INCREMENT,
    item_code       VARCHAR(64)            NOT NULL,
    item_name       VARCHAR(128)           NULL,
    item_synonym    VARCHAR(128)           NULL,
    parent_id       BIGINT UNSIGNED        NULL,
    tree            ENUM ('BRANCH','LEAF') NULL,
    -- audit fields
    created_at      DATETIME(3)            NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_at      DATETIME(3)            NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    deleted_at      DATETIME(3)            NULL,
    created_by      BIGINT UNSIGNED        NULL,
    updated_by      BIGINT UNSIGNED        NULL,
    deleted_by      BIGINT UNSIGNED        NULL,
    created_by_name VARCHAR(128)           NULL,
    updated_by_name VARCHAR(128)           NULL,
    deleted_by_name VARCHAR(128)           NULL,

    -- kunci & relasi
    CONSTRAINT pk_ai PRIMARY KEY (id),
    CONSTRAINT uk_ai_code UNIQUE (item_code),
    CONSTRAINT fk_ai_parent
        FOREIGN KEY (parent_id) REFERENCES aspak_items (id)
            ON UPDATE CASCADE ON DELETE SET NULL
)
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci;

-- Index kinerja
CREATE INDEX idx_ai_parent_tree ON aspak_items (parent_id, tree);
CREATE INDEX idx_ai_deleted_at ON aspak_items (deleted_at);
-- Prefix search (LIKE 'foo%')
CREATE INDEX idx_ai_name_prefix ON aspak_items (item_name);
CREATE INDEX idx_ai_syn_prefix ON aspak_items (item_synonym);
-- Fulltext search (LIKE '%foo%' pada nama/sinonim) — aktifkan jika dibutuhkan
ALTER TABLE aspak_items
    ADD FULLTEXT INDEX ftx_ai_text (item_name, item_synonym);


-- =========================================================
-- TABEL: aspak_programs
-- =========================================================
DROP TABLE IF EXISTS aspak_programs;
CREATE TABLE aspak_programs
(
    id              BIGINT UNSIGNED                            NOT NULL AUTO_INCREMENT,
    program_code    VARCHAR(64)                                NOT NULL, -- kode bisa untuk program/kegiatan/sub
    program_name    VARCHAR(256)                               NULL,     -- nama program/kegiatan/sub
    type            ENUM ('PROGRAM','ACTIVITY','SUB_ACTIVITY') NOT NULL,
    parent_id       BIGINT UNSIGNED                            NULL,     -- relasi ke induk (null untuk PROGRAM)
    tree            ENUM ('BRANCH','LEAF')                     NULL,     -- sebagai penguat struktur pohon

    -- audit fields
    created_at      DATETIME(3)                                NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    updated_at      DATETIME(3)                                NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    deleted_at      DATETIME(3)                                NULL,
    created_by      BIGINT UNSIGNED                            NULL,
    updated_by      BIGINT UNSIGNED                            NULL,
    deleted_by      BIGINT UNSIGNED                            NULL,
    created_by_name VARCHAR(128)                               NULL,
    updated_by_name VARCHAR(128)                               NULL,
    deleted_by_name VARCHAR(128)                               NULL,

    CONSTRAINT pk_ap PRIMARY KEY (id),
    CONSTRAINT uk_ap_code UNIQUE (program_code),
    CONSTRAINT fk_ap_parent
        FOREIGN KEY (parent_id) REFERENCES aspak_programs (id)
            ON UPDATE CASCADE ON DELETE SET NULL
)
    ENGINE = InnoDB
    ROW_FORMAT = DYNAMIC
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci;

-- Index kinerja umum
CREATE INDEX idx_ap_parent_type ON aspak_programs (parent_id, type);
CREATE INDEX idx_ap_deleted_at ON aspak_programs (deleted_at);
-- Prefix search (LIKE 'foo%')
CREATE INDEX idx_ap_name_prefix ON aspak_programs (program_name);
-- Fulltext (untuk LIKE '%foo%') — opsional
ALTER TABLE aspak_programs
    ADD FULLTEXT INDEX ftx_ap_name (program_name);



ALTER TABLE `asset_entries`
ADD `default_image` text NULL AFTER `latest_balance`;



START TRANSACTION;

-- =========================================
-- TABEL: sensi.asset_entries
-- =========================================
ALTER TABLE sensi.asset_entries
    ADD COLUMN IF NOT EXISTS aspak_description VARCHAR(256) NULL AFTER description;

-- =========================================
-- TABEL: sensi.assets
-- =========================================
ALTER TABLE sensi.assets
    -- Relasi/master ASPAK (opsional dijadikan FK nanti)
    ADD COLUMN IF NOT EXISTS aspak_item_id BIGINT NULL AFTER item_id,
    ADD COLUMN IF NOT EXISTS aspak_service_room_id BIGINT NULL AFTER document_room_id,

    -- Atribut ASPAK
    ADD COLUMN IF NOT EXISTS aspak_tool VARCHAR(128) NULL AFTER aspak_service_room_id,
    ADD COLUMN IF NOT EXISTS aspak_image_url VARCHAR(512) NULL AFTER aspak_tool,

    -- Klasifikasi Produk & Daya
    ADD COLUMN IF NOT EXISTS product_origin ENUM('DOMESTIC','FOREIGN') NULL AFTER default_image,
    ADD COLUMN IF NOT EXISTS power_source ENUM('AC','DC','NONE') NULL AFTER product_origin,
    ADD COLUMN IF NOT EXISTS connected_ups TINYINT(1) NOT NULL DEFAULT 0 AFTER power_source, -- boolean: 0=false, 1=true

-- Info tambahan
    ADD COLUMN IF NOT EXISTS information_tkdn VARCHAR(64) NULL AFTER connected_ups,
    ADD COLUMN IF NOT EXISTS risk_class VARCHAR(64) NULL AFTER information_tkdn,
    ADD COLUMN IF NOT EXISTS technical_lifetime_year SMALLINT UNSIGNED NULL AFTER risk_class;

-- Index bantu untuk kolom relasi baru (aman di-run berulang)
ALTER TABLE sensi.assets
    ADD INDEX IF NOT EXISTS idx_assets_aspak_item_id (aspak_item_id),
    ADD INDEX IF NOT EXISTS idx_assets_aspak_service_room_id (aspak_service_room_id);

COMMIT;
