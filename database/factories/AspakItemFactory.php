<?php

namespace Database\Factories;

use App\Models\AspakItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AspakItem>
 */
class AspakItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AspakItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'item_name' => $this->faker->words(2, true),
            'item_code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{3}'),
            'item_synonym' => $this->faker->sentence(3),
            'tree' => $this->faker->randomElement(['BRANCH', 'LEAF']),
            'parent_id' => null,
            'created_by' => null,
            'updated_by' => null,
        ];
    }

    /**
     * Indicate that the item is a BRANCH.
     */
    public function branch(): static
    {
        return $this->state(fn (array $attributes) => [
            'tree' => 'BRANCH',
            'parent_id' => null,
        ]);
    }

    /**
     * Indicate that the item is a LEAF.
     */
    public function leaf(): static
    {
        return $this->state(fn (array $attributes) => [
            'tree' => 'LEAF',
        ]);
    }
}
