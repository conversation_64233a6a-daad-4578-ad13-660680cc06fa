<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermissionWebSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Modul
            ["name" => "Master Data", "guard_name" => "web"],
            ["name" => "Manajemen Aset", "guard_name" => "web"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "web"],
            ["name" => "Logistik", "guard_name" => "web"],
            ["name" => "Penyusutan", "guard_name" => "web"],
            ["name" => "Perencanaan", "guard_name" => "web"],
            // Data Master
            ["name" => "Role - View", "guard_name" => "web"],
            ["name" => "Role - Action", "guard_name" => "web"],
            ["name" => "Data Karyawan - View", "guard_name" => "web"],
            ["name" => "Data Karyawan - Action", "guard_name" => "web"],
            ["name" => "Data Petugas - View", "guard_name" => "web"],
            ["name" => "Data Petugas - Action", "guard_name" => "web"],
            ["name" => "Data UOM - View", "guard_name" => "web"],
            ["name" => "Data UOM - Action", "guard_name" => "web"],
            ["name" => "Data Kategori Ruangan - View", "guard_name" => "web"],
            ["name" => "Data Kategori Ruangan - Action", "guard_name" => "web"],
            ["name" => "Data Ruangan - View", "guard_name" => "web"],
            ["name" => "Data Ruangan - Action", "guard_name" => "web"],
            ["name" => "Data Kategori Barang - View", "guard_name" => "web"],
            ["name" => "Data Kategori Barang - Action", "guard_name" => "web"],
            ["name" => "Data Barang - View", "guard_name" => "web"],
            ["name" => "Data Barang - Action", "guard_name" => "web"],
            ["name" => "Data Distributor - View", "guard_name" => "web"],
            ["name" => "Data Distributor - Action", "guard_name" => "web"],
            ["name" => "Data Kategori Pemeliharaan - View", "guard_name" => "web"],
            ["name" => "Data Kategori Pemeliharaan - Action", "guard_name" => "web"],
            ["name" => "Config Rekapitulasi - View", "guard_name" => "web"],
            ["name" => "Config Rekapitulasi - Action", "guard_name" => "web"],
            // Manajemen Aset
            ["name" => "Data Aset - View", "guard_name" => "web"],
            ["name" => "Data Aset - Action", "guard_name" => "web"],
            ["name" => "Data Aset - Print", "guard_name" => "web"],
            ["name" => "Penempatan & Document - View", "guard_name" => "web"],
            ["name" => "Penempatan & Document - Action", "guard_name" => "web"],
            ["name" => "Buku Bantu - View", "guard_name" => "web"],
            ["name" => "Buku Bantu - Action", "guard_name" => "web"],
            ["name" => "Kategori Aset - View", "guard_name" => "web"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "web"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "web"],
            ["name" => "Aset Rusak - View", "guard_name" => "web"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "web"],
            ["name" => "History Aset - View", "guard_name" => "web"],
            ["name" => "Laporan Penerimaan Inventaris - View", "guard_name" => "web"],
            ["name" => "Laporan Aspak - View", "guard_name" => "web"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "web"],
            ["name" => "Laporan KIR - View", "guard_name" => "web"],
            // Pemeliharaan Aset
            ["name" => "Jadwal Alkes - View", "guard_name" => "web"],
            ["name" => "Jadwal Alkes - Action", "guard_name" => "web"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "web"],
            ["name" => "Pemeliharaan Alkes - Action", "guard_name" => "web"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "web"],
            ["name" => "Jadwal Non Alkes - Action", "guard_name" => "web"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "web"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "web"],
            ["name" => "Request Perbaikan - View", "guard_name" => "web"],
            ["name" => "Request Perbaikan - Action", "guard_name" => "web"],
            ["name" => "Aktivitas Perbaikan - View", "guard_name" => "web"],
            ["name" => "Aktivitas Perbaikan - Action", "guard_name" => "web"],
            ["name" => "Putusan Perbaikan - View", "guard_name" => "web"],
            ["name" => "Putusan Perbaikan - Action", "guard_name" => "web"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "web"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "web"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "web"],
            ["name" => "Laporan Isidental - View", "guard_name" => "web"],
            // Logistik
            ["name" => "Daftar Aset Logistik - View", "guard_name" => "web"],
            ["name" => "Daftar Aset Logistik - Action", "guard_name" => "web"],
            ["name" => "Barang Masuk - View", "guard_name" => "web"],
            ["name" => "Barang Masuk - Action", "guard_name" => "web"],
            ["name" => "Permintaan Barang - View", "guard_name" => "web"],
            ["name" => "Permintaan Barang - Action", "guard_name" => "web"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "web"],
            ["name" => "Realisasi Permintaan - Action", "guard_name" => "web"],
            ["name" => "Barang Keluar - View", "guard_name" => "web"],
            ["name" => "Barang Keluar - Action", "guard_name" => "web"],
            ["name" => "Penyesuaian Barang - View", "guard_name" => "web"],
            ["name" => "Penyesuaian Barang - Action", "guard_name" => "web"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "web"],
            ["name" => "Stock Opname Non Logistik - Action", "guard_name" => "web"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "web"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "web"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "web"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "web"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "web"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "web"],
            // Penyusutan
            ["name" => "Setting Penyutusan - View", "guard_name" => "web"],
            ["name" => "Laporan Penyusutan - View", "guard_name" => "web"],
            // Perencanaan
            ["name" => "Perencanaan - View", "guard_name" => "web"],
            ["name" => "Perencanaan - Action", "guard_name" => "web"],
            ["name" => "Approve Perencanaan - View", "guard_name" => "web"],
            ["name" => "Approve Perencanaan - Action", "guard_name" => "web"],
            ["name" => "Pemeliharaan - View", "guard_name" => "web"],
            ["name" => "Pemeliharaan - Action", "guard_name" => "web"],
            ["name" => "Approve Pemeliharaan - View", "guard_name" => "web"],
            ["name" => "Approve Pemeliharaan - Action", "guard_name" => "web"],
            ["name" => "Laporan Perencanaan - View", "guard_name" => "web"],
            ["name" => "Laporan Pemeliharaan Perencanaan - View", "guard_name" => "web"],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }
    }
}
