<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AccessRightSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('role_has_permissions')->truncate();

        $PJKIRpermissions = [
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],
            ["name" => "Perencanaan", "guard_name" => "employee"],

            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR Barang - View", "guard_name" => "employee"],

            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - Action", "guard_name" => "employee"],

            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - Action", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],

            ["name" => "Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - Action", "guard_name" => "employee"],
        ];

        foreach ($PJKIRpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 1,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $KBPPApermissions = [
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],
            ["name" => "Perencanaan", "guard_name" => "employee"],

            ["name" => "Data Aset - View", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - View", "guard_name" => "employee"],
            ["name" => "Buku Bantu - View", "guard_name" => "employee"],
            ["name" => "Kategori Aset - View", "guard_name" => "employee"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Aset Rusak - View", "guard_name" => "employee"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "employee"],
            ["name" => "History Aset - View", "guard_name" => "employee"],
            ["name" => "Laporan Penenerimaan Inventaris - View", "guard_name" => "employee"],
            ["name" => "Laporan Aspak - View", "guard_name" => "employee"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR Barang - View", "guard_name" => "employee"],

            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],

            ["name" => "Setting Penyutusan - View", "guard_name" => "employee"],
            ["name" => "Laporan Penyusutan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Perencanaan - View", "guard_name" => "employee"],
        ];

        foreach ($KBPPApermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 2,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $KBPPApermissions = [
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],
            ["name" => "Perencanaan", "guard_name" => "employee"],

            ["name" => "Data Aset - View", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - View", "guard_name" => "employee"],
            ["name" => "Buku Bantu - View", "guard_name" => "employee"],
            ["name" => "Kategori Aset - View", "guard_name" => "employee"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Aset Rusak - View", "guard_name" => "employee"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "employee"],
            ["name" => "History Aset - View", "guard_name" => "employee"],
            ["name" => "Laporan Penenerimaan Inventaris - View", "guard_name" => "employee"],
            ["name" => "Laporan Aspak - View", "guard_name" => "employee"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR Barang - View", "guard_name" => "employee"],

            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],

            ["name" => "Setting Penyutusan - View", "guard_name" => "employee"],
            ["name" => "Laporan Penyusutan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Perencanaan - View", "guard_name" => "employee"],
        ];

        foreach ($KBPPApermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 3,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $KILpermissions = [
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],
            ["name" => "Perencanaan", "guard_name" => "employee"],

            ["name" => "Data Aset - View", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - View", "guard_name" => "employee"],
            ["name" => "Buku Bantu - View", "guard_name" => "employee"],
            ["name" => "Kategori Aset - View", "guard_name" => "employee"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Aset Rusak - View", "guard_name" => "employee"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "employee"],
            ["name" => "History Aset - View", "guard_name" => "employee"],
            ["name" => "Laporan Penenerimaan Inventaris - View", "guard_name" => "employee"],
            ["name" => "Laporan Aspak - View", "guard_name" => "employee"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR Barang - View", "guard_name" => "employee"],

            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],

            ["name" => "Setting Penyutusan - View", "guard_name" => "employee"],
            ["name" => "Laporan Penyusutan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Perencanaan - View", "guard_name" => "employee"],
        ];

        foreach ($KILpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 4,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $PBPpermissions = [
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],
            ["name" => "Perencanaan", "guard_name" => "employee"],

            ["name" => "Data Aset - View", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - View", "guard_name" => "employee"],
            ["name" => "Buku Bantu - View", "guard_name" => "employee"],
            ["name" => "Kategori Aset - View", "guard_name" => "employee"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Aset Rusak - View", "guard_name" => "employee"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "employee"],
            ["name" => "History Aset - View", "guard_name" => "employee"],
            ["name" => "Laporan Penenerimaan Inventaris - View", "guard_name" => "employee"],
            ["name" => "Laporan Aspak - View", "guard_name" => "employee"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR Barang - View", "guard_name" => "employee"],

            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],

            ["name" => "Setting Penyutusan - View", "guard_name" => "employee"],
            ["name" => "Laporan Penyusutan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Perencanaan - View", "guard_name" => "employee"],
        ];

        foreach ($PBPpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 5,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $PPBPpermissions = [
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],

            // Manajemen Aset
            ["name" => "Data Aset - View", "guard_name" => "employee"],
            ["name" => "Data Aset - Action", "guard_name" => "employee"],
            ["name" => "Data Aset - Print", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - View", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - Action", "guard_name" => "employee"],
            ["name" => "Buku Bantu - View", "guard_name" => "employee"],
            ["name" => "Buku Bantu - Action", "guard_name" => "employee"],
            ["name" => "Kategori Aset - View", "guard_name" => "employee"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Aset Rusak - View", "guard_name" => "employee"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "employee"],
            ["name" => "History Aset - View", "guard_name" => "employee"],
            ["name" => "Laporan Penenerimaan Inventaris - View", "guard_name" => "employee"],
            ["name" => "Laporan Aspak - View", "guard_name" => "employee"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR Barang - View", "guard_name" => "employee"],

            // Logistik
            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Daftar Aset Logistik - Action", "guard_name" => "employee"],
            ["name" => "Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Barang Masuk - Action", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - Action", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - Action", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - Action", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - Action", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],

        ];

        foreach ($PPBPpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 6,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $SILpermissions = [
            ["name" => "Logistik", "guard_name" => "employee"],

            // Logistik
            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Daftar Aset Logistik - Action", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - Action", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - Action", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - Action", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - Action", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],

        ];

        foreach ($SILpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 7,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $KBPNMpermissions = [
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Jadwal Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Isidental - View", "guard_name" => "employee"],
        ];

        foreach ($KBPNMpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 8,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $KTPPSpermissions = [
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Jadwal Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Isidental - View", "guard_name" => "employee"],

        ];

        foreach ($KTPPSpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 9,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $KIPFRSpermissions = [
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Jadwal Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - Action", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Isidental - View", "guard_name" => "employee"],

        ];

        foreach ($KIPFRSpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 10,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $AIPFRSpermissions = [
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Jadwal Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - Action", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Isidental - View", "guard_name" => "employee"],

        ];

        foreach ($AIPFRSpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 11,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        $SIPFRSpermissions = [
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],

            // Pemeliharaan
            ["name" => "Jadwal Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - Action", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Isidental - View", "guard_name" => "employee"],

        ];

        foreach ($SIPFRSpermissions as $permission) {
            $permissionRecord = DB::table('permissions')
                ->where('name', $permission['name'])
                ->where('guard_name', $permission['guard_name'])
                ->first();

            if ($permissionRecord) {
                $permissionId = $permissionRecord->id;

                DB::table('role_has_permissions')->insert([
                    'permission_id' => $permissionId,
                    'role_id' => 12,
                ]);
            } else {
                Log::warning("Permission not found: {$permission['name']} with guard {$permission['guard_name']}");
            }
        }

        // Superadmin
        $superadmin = Role::where(["name" => "Superadmin", "guard_name" => "web"])->first();
        $permissionsWeb = Permission::where("guard_name", "web")->pluck("name");

        $superadmin->syncPermissions($permissionsWeb);

        $userAdmin = User::where("email", "<EMAIL>")->first();
        $userAdmin->assignRole($superadmin->name);
    }
}
