<?php

namespace Database\Seeders;

use App\Models\AspakItem;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class AspakItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        AspakItem::truncate();
        
        // Initialize Faker
        $faker = Faker::create('id_ID'); // Using Indonesian locale for more relevant data
        
        // Data arrays for realistic naming
        $rootCategories = [
            'Peralatan Kimia Klinik dan Toksikologi Klinik',
            'Peralatan Hematologi',
            'Peralatan Mikrobiologi',
            'Peralatan Imunologi',
            'Peralatan Patologi Anatomi',
            'Peralatan Radiologi',
            'Peralatan Fisioterapi',
            'Peralatan Bedah',
            'Peralatan Gawat Darurat',
            'Peralatan Rekam Medis'
        ];
        
        $branchCategories = [
            'Sistem Tes Kimia Klinik',
            'Alat Pengukur Elektrolit',
            'Alat Uji Hormon',
            'Alat Uji Enzim',
            'Sistem Tes Urinalisis'
        ];
        
        $leafCategories = [
            'Test sistem (strip & midstream)',
            'Test sistem (card)',
            'Test sistem (kit & cair)',
            'Alat uji elektrolit',
            'Alat uji enzim',
            'Alat uji hormon',
            'Alat uji protein',
            'Alat uji glukosa',
            'Alat uji lipid',
            'Alat uji fungsi hati'
        ];
        
        $synonyms = [
            'Alat uji kimia klinis',
            'Sistem tes diagnostik',
            'Alat analisis medis',
            'Instrumen laboratorium',
            'Peralatan diagnostik',
            'Alat uji biokimia',
            'Sistem deteksi medis',
            'Alat pengukur klinis',
            'Instrumentasi medis',
            'Peralatan tes darah'
        ];
        
        // Store all created records for efficient insertion
        $records = [];
        $timestamp = now();
        
        // Level 1: Root level - 10 entries (2-digit codes)
        $level1Records = [];
        for ($i = 1; $i <= 10; $i++) {
            $code = str_pad($i, 2, '0', STR_PAD_LEFT);
            $record = [
                'item_code' => $code,
                'item_name' => $rootCategories[($i - 1) % count($rootCategories)],
                'item_synonym' => null,
                'parent_id' => null,
                'tree' => 'BRANCH',
                'created_by' => 1,
                'updated_by' => 1,
                'created_by_name' => 'System',
                'updated_by_name' => 'System',
                'created_at' => $timestamp,
                'updated_at' => $timestamp,
            ];
            
            $records[] = $record;
            $level1Records[] = $record; // We'll need these for parent references
        }
        
        // Level 2: Branch level - 5 entries per root (4-digit codes)
        $level2Records = [];
        $branchCounter = 1;
        for ($i = 0; $i < count($level1Records); $i++) { // For each root
            $rootCode = $level1Records[$i]['item_code'];
            $rootId = $i + 1; // ID is 1-based
            
            for ($j = 1; $j <= 5; $j++) { // 5 branches per root
                $branchCode = $rootCode . str_pad($j, 2, '0', STR_PAD_LEFT);
                $record = [
                    'item_code' => $branchCode,
                    'item_name' => $branchCategories[($branchCounter - 1) % count($branchCategories)] . ' ' . $faker->word(),
                    'item_synonym' => $faker->randomElement($synonyms),
                    'parent_id' => $rootId, // Reference to root record
                    'tree' => 'BRANCH',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp,
                ];
                
                $records[] = $record;
                $level2Records[] = $record; // We'll need these for parent references
                $branchCounter++;
            }
        }
        
        // Level 3: Leaf level - 5 entries per branch (6-7 digit codes)
        $leafCounter = 1;
        for ($i = 0; $i < count($level2Records); $i++) { // For each branch
            $branchCode = $level2Records[$i]['item_code'];
            $parentId = 10 + $i + 1; // Root records (10) + current branch index + 1
            
            for ($j = 1; $j <= 5; $j++) { // 5 leaves per branch
                // Generate 6 or 7 digit codes
                $leafCode = $branchCode . str_pad($j, 2, '0', STR_PAD_LEFT);
                if ($j > 9) {
                    $leafCode = $branchCode . $j; // 7-digit code for numbers > 9
                }
                
                $record = [
                    'item_code' => $leafCode,
                    'item_name' => $leafCategories[($leafCounter - 1) % count($leafCategories)] . ' ' . $faker->word(),
                    'item_synonym' => $faker->randomElement($synonyms) . ' ' . $faker->word(),
                    'parent_id' => $parentId, // Reference to branch record
                    'tree' => 'LEAF',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp,
                ];
                
                $records[] = $record;
                $leafCounter++;
            }
        }
        
        // Batch insert all records for better performance
        foreach (array_chunk($records, 500) as $chunk) {
            DB::table('aspak_items')->insert($chunk);
        }
        
        $this->command->info('AspakItem seeder completed.');
        $this->command->info('Total records: ' . count($records));
        $this->command->info('Roots (2-digit): ' . AspakItem::whereNull('parent_id')->count());
        $this->command->info('Branches (4-digit): ' . AspakItem::where('tree', 'BRANCH')->whereNotNull('parent_id')->count());
        $this->command->info('Leaves (6-7 digit): ' . AspakItem::where('tree', 'LEAF')->count());
    }
}