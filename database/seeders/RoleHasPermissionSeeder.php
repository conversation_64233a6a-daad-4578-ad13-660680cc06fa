<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleHasPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Superadmin
        $role = Role::where(['name' => 'Superadmin', "guard_name" => "web"])->first();
        $permissions = Permission::where("guard_name", "web")->pluck("name");

        $role->syncPermissions($permissions);

        // PJKIR
        $role = Role::where(['role_coder' => 'PJKIR', "guard_name" => "employee"])->first();
        $permissions = ["Manajemen Aset", "Data Aset - View", "Data Aset - Action",];
    }
}
