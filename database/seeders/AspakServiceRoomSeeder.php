<?php

namespace Database\Seeders;

use App\Models\AspakServiceRoom;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class AspakServiceRoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        AspakServiceRoom::truncate();
        
        // Initialize Faker
        $faker = Faker::create('id_ID'); // Using Indonesian locale for more relevant data
        
        // Data arrays for realistic naming
        $rootCategories = ['Rawat Inap', 'Rawat Jalan', 'IGD', 'Radiologi', 'Laboratorium', 'Farmasi', 'Rehabilitasi', 'ICU', 'Hemodialisis', 'Bedah'];
        $branchCategories = ['Kamar', 'Poli', 'Unit', 'Laboratorium', 'Radiologi', 'Instalasi', 'Ruang', 'Area', 'Zona', 'Seksi'];
        $leafCategories = ['Tempat Tidur', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Monitor', 'Infus', '<PERSON>sigen'];
        
        $services = ['Pelayanan', 'Perawatan', 'Tindakan', 'Konsultasi', 'Pemeriksaan', 'Terapi', 'Monitoring', 'Edukasi', 'Rehabilitasi', 'Evaluasi'];
        $details = ['Darurat', 'Rutin', 'Khusus', 'Komprehensif', 'Lanjutan', 'Dasar', 'Spesialis', 'Umum', 'Intensif', 'Berkala'];
        
        // Store all created records for efficient insertion
        $records = [];
        $timestamp = now();
        
        // Level 1: Root level - 10 entries (2-digit codes)
        $level1Records = [];
        for ($i = 1; $i <= 10; $i++) {
            $code = str_pad($i, 2, '0', STR_PAD_LEFT);
            $record = [
                'room_service_code' => $code,
                'room_service_name' => $rootCategories[($i - 1) % count($rootCategories)] . ' ' . $faker->word(),
                'parent_id' => null,
                'tree' => 'BRANCH',
                'created_by' => 1,
                'updated_by' => 1,
                'created_by_name' => 'System',
                'updated_by_name' => 'System',
                'created_at' => $timestamp,
                'updated_at' => $timestamp,
            ];
            
            $records[] = $record;
            $level1Records[] = $record; // We'll need these for parent references
        }
        
        // Level 2: Branch level - 100 entries (4-digit codes)
        $level2Records = [];
        $branchCounter = 1;
        for ($i = 1; $i <= 10; $i++) { // For each root
            $rootCode = str_pad($i, 2, '0', STR_PAD_LEFT);
            
            for ($j = 1; $j <= 10; $j++) { // 10 branches per root
                $branchCode = $rootCode . str_pad($j, 2, '0', STR_PAD_LEFT);
                $record = [
                    'room_service_code' => $branchCode,
                    'room_service_name' => $branchCategories[($branchCounter - 1) % count($branchCategories)] . ' ' . $services[($branchCounter - 1) % count($services)] . ' ' . $faker->word(),
                    'parent_id' => $i, // Reference to root record
                    'tree' => 'BRANCH',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp,
                ];
                
                $records[] = $record;
                $level2Records[] = $record; // We'll need these for parent references
                $branchCounter++;
            }
        }
        
        // Level 3: Leaf level - 500 entries (6-digit codes)
        $leafCounter = 1;
        for ($i = 0; $i < count($level2Records); $i++) { // For each branch
            $branchCode = $level2Records[$i]['room_service_code'];
            $parentId = 10 + $i + 1; // Root records (10) + current branch index + 1
            
            for ($j = 1; $j <= 5; $j++) { // 5 leaves per branch
                $leafCode = $branchCode . str_pad($j, 2, '0', STR_PAD_LEFT);
                $record = [
                    'room_service_code' => $leafCode,
                    'room_service_name' => $leafCategories[($leafCounter - 1) % count($leafCategories)] . ' ' . $details[($leafCounter - 1) % count($details)] . ' ' . $faker->word(),
                    'parent_id' => $parentId, // Reference to branch record
                    'tree' => 'LEAF',
                    'created_by' => 1,
                    'updated_by' => 1,
                    'created_by_name' => 'System',
                    'updated_by_name' => 'System',
                    'created_at' => $timestamp,
                    'updated_at' => $timestamp,
                ];
                
                $records[] = $record;
                $leafCounter++;
            }
        }
        
        // Batch insert all records for better performance
        foreach (array_chunk($records, 500) as $chunk) {
            DB::table('aspak_service_rooms')->insert($chunk);
        }
        
        $this->command->info('AspakServiceRoom seeder completed.');
        $this->command->info('Total records: ' . count($records));
        $this->command->info('Roots (2-digit): ' . AspakServiceRoom::whereNull('parent_id')->count());
        $this->command->info('Branches (4-digit): ' . AspakServiceRoom::where('tree', 'BRANCH')->whereNotNull('parent_id')->count());
        $this->command->info('Leaves (6-digit): ' . AspakServiceRoom::where('tree', 'LEAF')->count());
    }
}