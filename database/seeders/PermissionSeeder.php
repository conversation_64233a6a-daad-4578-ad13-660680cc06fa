<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Modul
            ["name" => "Master Data", "guard_name" => "employee"],
            ["name" => "Manajemen Aset", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Aset", "guard_name" => "employee"],
            ["name" => "Logistik", "guard_name" => "employee"],
            ["name" => "Penyusu<PERSON>", "guard_name" => "employee"],
            ["name" => "Perencanaan", "guard_name" => "employee"],
            // Data Master
            ["name" => "Role - View", "guard_name" => "employee"],
            ["name" => "Role - Action", "guard_name" => "employee"],
            ["name" => "Data Karyawan - View", "guard_name" => "employee"],
            ["name" => "Data Karyawan - Action", "guard_name" => "employee"],
            ["name" => "Data Petugas - View", "guard_name" => "employee"],
            ["name" => "Data Petugas - Action", "guard_name" => "employee"],
            ["name" => "Data UOM - View", "guard_name" => "employee"],
            ["name" => "Data UOM - Action", "guard_name" => "employee"],
            ["name" => "Data Kategori Ruangan - View", "guard_name" => "employee"],
            ["name" => "Data Kategori Ruangan - Action", "guard_name" => "employee"],
            ["name" => "Data Ruangan - View", "guard_name" => "employee"],
            ["name" => "Data Ruangan - Action", "guard_name" => "employee"],
            ["name" => "Data Kategori Barang - View", "guard_name" => "employee"],
            ["name" => "Data Kategori Barang - Action", "guard_name" => "employee"],
            ["name" => "Data Barang - View", "guard_name" => "employee"],
            ["name" => "Data Barang - Action", "guard_name" => "employee"],
            ["name" => "Data Distributor - View", "guard_name" => "employee"],
            ["name" => "Data Distributor - Action", "guard_name" => "employee"],
            ["name" => "Data Kategori Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Data Kategori Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Config Rekapitulasi - View", "guard_name" => "employee"],
            ["name" => "Config Rekapitulasi - Action", "guard_name" => "employee"],
            // Manajemen Aset
            ["name" => "Data Aset - View", "guard_name" => "employee"],
            ["name" => "Data Aset - Action", "guard_name" => "employee"],
            ["name" => "Data Aset - Print", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - View", "guard_name" => "employee"],
            ["name" => "Penempatan & Document - Action", "guard_name" => "employee"],
            ["name" => "Buku Bantu - View", "guard_name" => "employee"],
            ["name" => "Buku Bantu - Action", "guard_name" => "employee"],
            ["name" => "Kategori Aset - View", "guard_name" => "employee"],
            ["name" => "Kartu Inventaris Ruangan - View", "guard_name" => "employee"],
            ["name" => "Perubahan Posisi - View", "guard_name" => "employee"],
            ["name" => "Aset Rusak - View", "guard_name" => "employee"],
            ["name" => "Penghapusan Aset - View", "guard_name" => "employee"],
            ["name" => "History Aset - View", "guard_name" => "employee"],
            ["name" => "Laporan Penerimaan Inventaris - View", "guard_name" => "employee"],
            ["name" => "Laporan Aspak - View", "guard_name" => "employee"],
            ["name" => "Laporan Daftar Barang - View", "guard_name" => "employee"],
            ["name" => "Laporan KIR - View", "guard_name" => "employee"],
            // Pemeliharaan Aset
            ["name" => "Jadwal Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Alkes - Action", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Jadwal Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan Non Alkes - Action", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Request Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Aktivitas Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - View", "guard_name" => "employee"],
            ["name" => "Putusan Perbaikan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Jadwal - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Detail Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Isidental - View", "guard_name" => "employee"],
            // Logistik
            ["name" => "Daftar Aset Logistik - View", "guard_name" => "employee"],
            ["name" => "Daftar Aset Logistik - Action", "guard_name" => "employee"],
            ["name" => "Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Barang Masuk - Action", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - View", "guard_name" => "employee"],
            ["name" => "Permintaan Barang - Action", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - View", "guard_name" => "employee"],
            ["name" => "Realisasi Permintaan - Action", "guard_name" => "employee"],
            ["name" => "Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Barang Keluar - Action", "guard_name" => "employee"],
            ["name" => "Penyesuaian Barang - View", "guard_name" => "web"],
            ["name" => "Penyesuaian Barang - Action", "guard_name" => "web"],
            ["name" => "Stock Opname Non Logistik - View", "guard_name" => "employee"],
            ["name" => "Stock Opname Non Logistik - Action", "guard_name" => "employee"],
            ["name" => "Laporan Barang Masuk - View", "guard_name" => "employee"],
            ["name" => "Laporan Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Resume Barang Keluar - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname - View", "guard_name" => "employee"],
            ["name" => "Laporan Kartu Stock - View", "guard_name" => "employee"],
            ["name" => "Laporan Stock Opname Non Logistik - View", "guard_name" => "employee"],
            // Penyusutan
            ["name" => "Setting Penyutusan - View", "guard_name" => "employee"],
            ["name" => "Laporan Penyusutan - View", "guard_name" => "employee"],
            // Perencanaan
            ["name" => "Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Approve Perencanaan - Action", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - View", "guard_name" => "employee"],
            ["name" => "Approve Pemeliharaan - Action", "guard_name" => "employee"],
            ["name" => "Laporan Perencanaan - View", "guard_name" => "employee"],
            ["name" => "Laporan Pemeliharaan Perencanaan - View", "guard_name" => "employee"],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate($permission);
        }
    }
}
