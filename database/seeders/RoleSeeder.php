<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            ["name" => "Penanggung Jawab KIR", "guard_name" => "employee", "role_code" => "PJKIR"],
            ["name" => "Kepala Bagian Perencanaan Program & Aset", "guard_name" => "employee", "role_code" => "KBPPA"],
            ["name" => "Ketua Tim Kerja Aset", "guard_name" => "employee", "role_code" => "KTKA"],
            ["name" => "Kepala Instalasi Logistik", "guard_name" => "employee", "role_code" => "KIL"],
            ["name" => "Pengurus Barang Pengguna", "guard_name" => "employee", "role_code" => "PBP"],
            ["name" => "Pembantu Pengurus Barang Pengguna", "guard_name" => "employee", "role_code" => "PPBP"],
            ["name" => "Staf Instalasi Logistik", "guard_name" => "employee", "role_code" => "SIL"],
            ["name" => "Kepala Bidang Penunjang Non Medik", "guard_name" => "employee", "role_code" => "KBPNM"],
            ["name" => "Ketua Tim Penunjang Pengelolaan Sarpras", "guard_name" => "employee", "role_code" => "KTPPS"],
            ["name" => "Kepala IPFRS", "guard_name" => "employee", "role_code" => "KIPFRS"],
            ["name" => "Admin IPFRS", "guard_name" => "employee", "role_code" => "AIPFRS"],
            ["name" => "Staf IPFRS", "guard_name" => "employee", "role_code" => "SIPFRS"],
            ["name" => "Superadmin", "guard_name" => "web", "role_code" => "SU"],
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role['name'], 'guard_name' => $role['guard_name'], 'role_code' => $role['role_code']]);
        }
    }
}
